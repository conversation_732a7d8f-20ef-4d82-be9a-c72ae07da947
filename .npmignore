# Source files
src/
*.ts
!*.d.ts

# Development files
.env
.env.*
!.env.example

# Testing
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts
coverage/
jest.config.js

# Build tools
tsconfig.json
.eslintrc.*
.prettierrc.*

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Web directory (not needed for CLI)
web/

# Documentation source
docs/

# Git
.git/
.gitignore

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
