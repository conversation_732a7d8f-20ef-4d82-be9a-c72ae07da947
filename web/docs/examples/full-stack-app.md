---
sidebar_position: 3
---

# Full Stack Application Example

This example demonstrates how to create a complete full-stack application using Microgen with a modern frontend and robust backend API.

## Project Overview

We'll build a task management application with:

- **Backend**: NestJS API with PostgreSQL database
- **Frontend**: Next.js application with Tailwind CSS
- **Authentication**: JWT-based user authentication
- **Real-time**: WebSocket connections for live updates
- **Deployment**: Docker containerization

## Step 1: Generate the Project

```bash
microgen create task-manager --template full
```

**Interactive Setup:**
- Backend Framework: NestJS
- Database: PostgreSQL
- Frontend Framework: Next.js
- Styling: Tailwind CSS
- Authentication: JWT
- Real-time: WebSockets
- Docker: Yes

## Step 2: Project Structure

```
task-manager/
├── backend/
│   ├── src/
│   │   ├── auth/
│   │   ├── tasks/
│   │   ├── users/
│   │   ├── websocket/
│   │   └── main.ts
│   ├── Dockerfile
│   └── package.json
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── services/
│   │   └── types/
│   ├── Dockerfile
│   └── package.json
├── docker-compose.yml
└── README.md
```

## Step 3: Backend Implementation

### Database Schema

```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  avatar_url VARCHAR(500),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Tasks table
CREATE TABLE tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  status VARCHAR(50) DEFAULT 'todo',
  priority VARCHAR(50) DEFAULT 'medium',
  due_date TIMESTAMP,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Task comments table
CREATE TABLE task_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  content TEXT NOT NULL,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Task Entity

```typescript
// backend/src/tasks/entities/task.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { TaskComment } from './task-comment.entity';

export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  DONE = 'done'
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

@Entity('tasks')
export class Task {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: TaskStatus,
    default: TaskStatus.TODO
  })
  status: TaskStatus;

  @Column({
    type: 'enum',
    enum: TaskPriority,
    default: TaskPriority.MEDIUM
  })
  priority: TaskPriority;

  @Column({ type: 'timestamp', nullable: true })
  dueDate: Date;

  @ManyToOne(() => User, user => user.tasks)
  user: User;

  @Column()
  userId: string;

  @OneToMany(() => TaskComment, comment => comment.task)
  comments: TaskComment[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

### Task Service

```typescript
// backend/src/tasks/tasks.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Task, TaskStatus } from './entities/task.entity';
import { CreateTaskDto, UpdateTaskDto, TaskFiltersDto } from './dto';
import { WebSocketGateway } from '../websocket/websocket.gateway';

@Injectable()
export class TasksService {
  constructor(
    @InjectRepository(Task)
    private taskRepository: Repository<Task>,
    private websocketGateway: WebSocketGateway,
  ) {}

  async create(createTaskDto: CreateTaskDto, userId: string): Promise<Task> {
    const task = this.taskRepository.create({
      ...createTaskDto,
      userId,
    });

    const savedTask = await this.taskRepository.save(task);
    
    // Emit real-time update
    this.websocketGateway.emitTaskCreated(savedTask);
    
    return savedTask;
  }

  async findAll(userId: string, filters: TaskFiltersDto): Promise<Task[]> {
    const query = this.taskRepository
      .createQueryBuilder('task')
      .where('task.userId = :userId', { userId })
      .leftJoinAndSelect('task.comments', 'comments')
      .leftJoinAndSelect('comments.user', 'commentUser');

    if (filters.status) {
      query.andWhere('task.status = :status', { status: filters.status });
    }

    if (filters.priority) {
      query.andWhere('task.priority = :priority', { priority: filters.priority });
    }

    if (filters.search) {
      query.andWhere(
        '(task.title ILIKE :search OR task.description ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    return query
      .orderBy('task.createdAt', 'DESC')
      .getMany();
  }

  async findOne(id: string, userId: string): Promise<Task> {
    const task = await this.taskRepository.findOne({
      where: { id, userId },
      relations: ['comments', 'comments.user'],
    });

    if (!task) {
      throw new NotFoundException('Task not found');
    }

    return task;
  }

  async update(id: string, updateTaskDto: UpdateTaskDto, userId: string): Promise<Task> {
    const task = await this.findOne(id, userId);
    
    Object.assign(task, updateTaskDto);
    const updatedTask = await this.taskRepository.save(task);
    
    // Emit real-time update
    this.websocketGateway.emitTaskUpdated(updatedTask);
    
    return updatedTask;
  }

  async remove(id: string, userId: string): Promise<void> {
    const task = await this.findOne(id, userId);
    await this.taskRepository.remove(task);
    
    // Emit real-time update
    this.websocketGateway.emitTaskDeleted(id);
  }

  async updateStatus(id: string, status: TaskStatus, userId: string): Promise<Task> {
    return this.update(id, { status }, userId);
  }
}
```

### WebSocket Gateway

```typescript
// backend/src/websocket/websocket.gateway.ts
import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Task } from '../tasks/entities/task.entity';

@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
})
export class WebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private connectedUsers = new Map<string, string>(); // socketId -> userId

  handleConnection(client: Socket) {
    console.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    console.log(`Client disconnected: ${client.id}`);
    this.connectedUsers.delete(client.id);
  }

  @SubscribeMessage('join')
  handleJoin(client: Socket, userId: string) {
    this.connectedUsers.set(client.id, userId);
    client.join(`user:${userId}`);
  }

  emitTaskCreated(task: Task) {
    this.server.to(`user:${task.userId}`).emit('taskCreated', task);
  }

  emitTaskUpdated(task: Task) {
    this.server.to(`user:${task.userId}`).emit('taskUpdated', task);
  }

  emitTaskDeleted(taskId: string) {
    this.server.emit('taskDeleted', { id: taskId });
  }
}
```

## Step 4: Frontend Implementation

### API Service

```typescript
// frontend/src/services/api.ts
import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Request interceptor
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const taskService = {
  getTasks: (filters?: TaskFilters) => 
    apiClient.get('/tasks', { params: filters }),
  
  getTask: (id: string) => 
    apiClient.get(`/tasks/${id}`),
  
  createTask: (data: CreateTaskData) => 
    apiClient.post('/tasks', data),
  
  updateTask: (id: string, data: UpdateTaskData) => 
    apiClient.put(`/tasks/${id}`, data),
  
  deleteTask: (id: string) => 
    apiClient.delete(`/tasks/${id}`),
  
  updateTaskStatus: (id: string, status: TaskStatus) => 
    apiClient.patch(`/tasks/${id}/status`, { status }),
};

export const authService = {
  login: (credentials: LoginCredentials) => 
    apiClient.post('/auth/login', credentials),
  
  register: (data: RegisterData) => 
    apiClient.post('/auth/register', data),
  
  getProfile: () => 
    apiClient.get('/auth/profile'),
  
  refreshToken: () => 
    apiClient.post('/auth/refresh'),
};
```

### WebSocket Hook

```typescript
// frontend/src/hooks/useWebSocket.ts
import { useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './useAuth';

export const useWebSocket = () => {
  const socketRef = useRef<Socket | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    if (!user) return;

    const socket = io(process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:3001');
    socketRef.current = socket;

    socket.on('connect', () => {
      console.log('Connected to WebSocket');
      socket.emit('join', user.id);
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from WebSocket');
    });

    return () => {
      socket.disconnect();
    };
  }, [user]);

  const subscribe = (event: string, callback: (data: any) => void) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback);
    }
  };

  const unsubscribe = (event: string) => {
    if (socketRef.current) {
      socketRef.current.off(event);
    }
  };

  return { subscribe, unsubscribe };
};
```

### Task Board Component

```typescript
// frontend/src/components/TaskBoard.tsx
import React, { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { Task, TaskStatus } from '../types/task';
import { taskService } from '../services/api';
import { useWebSocket } from '../hooks/useWebSocket';
import TaskCard from './TaskCard';
import CreateTaskModal from './CreateTaskModal';

const COLUMNS = [
  { id: TaskStatus.TODO, title: 'To Do', color: 'bg-gray-100' },
  { id: TaskStatus.IN_PROGRESS, title: 'In Progress', color: 'bg-blue-100' },
  { id: TaskStatus.DONE, title: 'Done', color: 'bg-green-100' },
];

export default function TaskBoard() {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const { subscribe, unsubscribe } = useWebSocket();

  useEffect(() => {
    loadTasks();
    
    // Subscribe to real-time updates
    subscribe('taskCreated', (task: Task) => {
      setTasks(prev => [task, ...prev]);
    });

    subscribe('taskUpdated', (task: Task) => {
      setTasks(prev => prev.map(t => t.id === task.id ? task : t));
    });

    subscribe('taskDeleted', ({ id }: { id: string }) => {
      setTasks(prev => prev.filter(t => t.id !== id));
    });

    return () => {
      unsubscribe('taskCreated');
      unsubscribe('taskUpdated');
      unsubscribe('taskDeleted');
    };
  }, []);

  const loadTasks = async () => {
    try {
      const response = await taskService.getTasks();
      setTasks(response.data);
    } catch (error) {
      console.error('Failed to load tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDragEnd = async (result: any) => {
    if (!result.destination) return;

    const { draggableId, destination } = result;
    const newStatus = destination.droppableId as TaskStatus;

    try {
      await taskService.updateTaskStatus(draggableId, newStatus);
      // Real-time update will handle UI update
    } catch (error) {
      console.error('Failed to update task status:', error);
    }
  };

  const getTasksByStatus = (status: TaskStatus) => 
    tasks.filter(task => task.status === status);

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Task Board</h1>
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          Add Task
        </button>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {COLUMNS.map(column => (
            <div key={column.id} className={`${column.color} rounded-lg p-4`}>
              <h2 className="font-semibold text-lg mb-4">{column.title}</h2>
              
              <Droppable droppableId={column.id}>
                {(provided) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className="space-y-3 min-h-[200px]"
                  >
                    {getTasksByStatus(column.id).map((task, index) => (
                      <Draggable key={task.id} draggableId={task.id} index={index}>
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                          >
                            <TaskCard task={task} />
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
          ))}
        </div>
      </DragDropContext>

      {showCreateModal && (
        <CreateTaskModal
          onClose={() => setShowCreateModal(false)}
          onTaskCreated={loadTasks}
        />
      )}
    </div>
  );
}
```

## Step 5: Docker Configuration

```yaml
# docker-compose.yml
version: '3.8'

services:
  # Database
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=taskmanager
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  # Backend
  backend:
    build: ./backend
    ports:
      - "3001:3000"
    environment:
      - DATABASE_URL=****************************************/taskmanager
      - JWT_SECRET=your-jwt-secret
      - FRONTEND_URL=http://localhost:3000
    depends_on:
      - postgres
    volumes:
      - ./backend:/app
      - /app/node_modules

  # Frontend
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:3001
      - NEXT_PUBLIC_WS_URL=http://localhost:3001
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules

volumes:
  postgres_data:
```

## Step 6: Running the Application

```bash
# Start all services
docker-compose up -d

# Check logs
docker-compose logs -f

# Access the application
open http://localhost:3000

# API documentation
open http://localhost:3001/api/docs
```

## Features Included

### Authentication
- JWT-based authentication
- Protected routes
- User registration and login
- Password hashing with bcrypt

### Real-time Updates
- WebSocket connections
- Live task updates
- Collaborative editing
- Connection management

### Task Management
- Create, read, update, delete tasks
- Drag-and-drop status updates
- Task filtering and search
- Due date management
- Priority levels

### Modern UI
- Responsive design with Tailwind CSS
- Drag-and-drop interface
- Modal dialogs
- Loading states
- Error handling

## Next Steps

- [**Microservice Architecture**](./microservice-architecture) - Scale to multiple services
- [**Basic API Example**](./basic-api) - Simple API implementation
- [**Deployment Guide**](../deployment/docker) - Production deployment
- [**Configuration Options**](../configuration/project-config) - Customize your setup
