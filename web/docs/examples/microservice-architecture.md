---
sidebar_position: 2
---

# Microservice Architecture Example

This example demonstrates how to build a complete microservice architecture using Microgen with multiple services, databases, and communication patterns.

## Architecture Overview

We'll build an e-commerce platform with the following services:

- **API Gateway** - Request routing and authentication
- **User Service** - User management and authentication
- **Product Service** - Product catalog and inventory
- **Order Service** - Order processing and management
- **Notification Service** - Email and push notifications
- **Frontend App** - React-based web application

## Project Structure

```
ecommerce-platform/
├── services/
│   ├── api-gateway/
│   ├── user-service/
│   ├── product-service/
│   ├── order-service/
│   └── notification-service/
├── apps/
│   └── web-app/
├── shared/
│   ├── types/
│   └── utils/
├── docker-compose.yml
└── README.md
```

## Step 1: Generate Services

Create each service using Microgen:

```bash
# API Gateway
microgen create api-gateway --template api-only
# Select: Express.js, PostgreSQL, Redis

# User Service
microgen create user-service --template api-only
# Select: NestJS, PostgreSQL, Kafka + gRPC

# Product Service
microgen create product-service --template api-only
# Select: NestJS, PostgreSQL, Kafka + gRPC

# Order Service
microgen create order-service --template api-only
# Select: NestJS, PostgreSQL, Kafka + gRPC

# Notification Service
microgen create notification-service --template api-only
# Select: NestJS, MongoDB, Kafka

# Frontend Application
microgen create web-app --template frontend-only
# Select: Next.js, Tailwind CSS
```

## Step 2: Service Communication

### Event-Driven Architecture with Kafka

**User Service Events:**
```typescript
// user-service/src/events/user.events.ts
export interface UserCreatedEvent {
  userId: string;
  email: string;
  name: string;
  timestamp: Date;
}

export interface UserUpdatedEvent {
  userId: string;
  changes: Partial<User>;
  timestamp: Date;
}
```

**Order Service Events:**
```typescript
// order-service/src/events/order.events.ts
export interface OrderCreatedEvent {
  orderId: string;
  userId: string;
  items: OrderItem[];
  total: number;
  timestamp: Date;
}

export interface OrderStatusChangedEvent {
  orderId: string;
  status: OrderStatus;
  timestamp: Date;
}
```

### gRPC Service-to-Service Communication

**User Service gRPC Definition:**
```protobuf
// shared/proto/user.proto
syntax = "proto3";

service UserService {
  rpc GetUser(GetUserRequest) returns (User);
  rpc ValidateUser(ValidateUserRequest) returns (ValidationResponse);
}

message User {
  string id = 1;
  string email = 2;
  string name = 3;
  bool active = 4;
}

message GetUserRequest {
  string user_id = 1;
}

message ValidateUserRequest {
  string token = 1;
}

message ValidationResponse {
  bool valid = 1;
  User user = 2;
}
```

## Step 3: Database Design

### User Service Schema
```sql
-- users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- user_profiles table
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  phone VARCHAR(50),
  address JSONB,
  preferences JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Product Service Schema
```sql
-- categories table
CREATE TABLE categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  parent_id UUID REFERENCES categories(id),
  created_at TIMESTAMP DEFAULT NOW()
);

-- products table
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  category_id UUID REFERENCES categories(id),
  inventory_count INTEGER DEFAULT 0,
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Order Service Schema
```sql
-- orders table
CREATE TABLE orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  status VARCHAR(50) DEFAULT 'pending',
  total_amount DECIMAL(10,2) NOT NULL,
  shipping_address JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- order_items table
CREATE TABLE order_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  product_id UUID NOT NULL,
  quantity INTEGER NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL
);
```

## Step 4: API Gateway Configuration

```typescript
// api-gateway/src/routes/index.ts
import express from 'express';
import { createProxyMiddleware } from 'http-proxy-middleware';
import { authMiddleware } from '../middleware/auth';

const router = express.Router();

// User service routes
router.use('/api/users', 
  authMiddleware,
  createProxyMiddleware({
    target: process.env.USER_SERVICE_URL,
    changeOrigin: true,
    pathRewrite: { '^/api/users': '' }
  })
);

// Product service routes
router.use('/api/products',
  createProxyMiddleware({
    target: process.env.PRODUCT_SERVICE_URL,
    changeOrigin: true,
    pathRewrite: { '^/api/products': '' }
  })
);

// Order service routes
router.use('/api/orders',
  authMiddleware,
  createProxyMiddleware({
    target: process.env.ORDER_SERVICE_URL,
    changeOrigin: true,
    pathRewrite: { '^/api/orders': '' }
  })
);

export default router;
```

## Step 5: Docker Compose Configuration

```yaml
version: '3.8'

services:
  # API Gateway
  api-gateway:
    build: ./services/api-gateway
    ports:
      - "3000:3000"
    environment:
      - USER_SERVICE_URL=http://user-service:3000
      - PRODUCT_SERVICE_URL=http://product-service:3000
      - ORDER_SERVICE_URL=http://order-service:3000
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - user-service
      - product-service
      - order-service

  # User Service
  user-service:
    build: ./services/user-service
    environment:
      - DATABASE_URL=***********************************/users
      - KAFKA_BROKERS=kafka:9092
      - GRPC_PORT=50051
    depends_on:
      - user-db
      - kafka

  user-db:
    image: postgres:13
    environment:
      - POSTGRES_DB=users
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - user_data:/var/lib/postgresql/data

  # Product Service
  product-service:
    build: ./services/product-service
    environment:
      - DATABASE_URL=**************************************/products
      - KAFKA_BROKERS=kafka:9092
      - GRPC_PORT=50052
    depends_on:
      - product-db
      - kafka

  product-db:
    image: postgres:13
    environment:
      - POSTGRES_DB=products
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - product_data:/var/lib/postgresql/data

  # Order Service
  order-service:
    build: ./services/order-service
    environment:
      - DATABASE_URL=************************************/orders
      - KAFKA_BROKERS=kafka:9092
      - USER_SERVICE_GRPC=user-service:50051
      - PRODUCT_SERVICE_GRPC=product-service:50052
      - GRPC_PORT=50053
    depends_on:
      - order-db
      - kafka
      - user-service
      - product-service

  order-db:
    image: postgres:13
    environment:
      - POSTGRES_DB=orders
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - order_data:/var/lib/postgresql/data

  # Notification Service
  notification-service:
    build: ./services/notification-service
    environment:
      - MONGODB_URL=mongodb://notification-db:27017/notifications
      - KAFKA_BROKERS=kafka:9092
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
    depends_on:
      - notification-db
      - kafka

  notification-db:
    image: mongo:5
    volumes:
      - notification_data:/data/db

  # Frontend Application
  web-app:
    build: ./apps/web-app
    ports:
      - "3001:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:3000/api
    depends_on:
      - api-gateway

  # Infrastructure
  kafka:
    image: confluentinc/cp-kafka:latest
    environment:
      - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
    depends_on:
      - zookeeper

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      - ZOOKEEPER_CLIENT_PORT=2181

  redis:
    image: redis:alpine
    volumes:
      - redis_data:/data

volumes:
  user_data:
  product_data:
  order_data:
  notification_data:
  redis_data:
```

## Step 6: Frontend Integration

```typescript
// web-app/src/services/api.ts
import axios from 'axios';

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 10000,
});

// Request interceptor for auth
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const userService = {
  login: (credentials: LoginCredentials) => 
    apiClient.post('/users/login', credentials),
  
  getProfile: () => 
    apiClient.get('/users/profile'),
  
  updateProfile: (data: UpdateProfileData) => 
    apiClient.put('/users/profile', data),
};

export const productService = {
  getProducts: (params?: ProductFilters) => 
    apiClient.get('/products', { params }),
  
  getProduct: (id: string) => 
    apiClient.get(`/products/${id}`),
};

export const orderService = {
  createOrder: (orderData: CreateOrderData) => 
    apiClient.post('/orders', orderData),
  
  getOrders: () => 
    apiClient.get('/orders'),
  
  getOrder: (id: string) => 
    apiClient.get(`/orders/${id}`),
};
```

## Step 7: Running the System

```bash
# Start all services
docker-compose up -d

# Check service health
curl http://localhost:3000/health

# Test user registration
curl -X POST http://localhost:3000/api/users/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password","name":"John Doe"}'

# Test product listing
curl http://localhost:3000/api/products

# Access frontend
open http://localhost:3001
```

## Monitoring and Observability

### Health Checks
Each service exposes health endpoints:

```typescript
// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'user-service',
    version: process.env.npm_package_version
  });
});
```

### Logging
Structured logging across all services:

```typescript
import winston from 'winston';

const logger = winston.createLogger({
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'app.log' })
  ]
});
```

## Next Steps

- [**Full Stack Example**](./full-stack-app) - Complete application example
- [**Basic API Example**](./basic-api) - Simple API service
- [**Configuration Guide**](../configuration/microservices) - Detailed configuration options
- [**Deployment Guide**](../deployment/docker) - Production deployment strategies
