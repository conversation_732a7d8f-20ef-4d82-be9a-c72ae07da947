---
sidebar_position: 10
---

# Contributing

We welcome contributions to Microgen! This guide will help you get started with contributing to the project.

## 🚀 Quick Start for Contributors

### 1. <PERSON> and <PERSON><PERSON>
```bash
# Fork the repository on GitHub, then clone your fork
git clone https://github.com/YOUR_USERNAME/microgen.git
cd microgen

# Add the original repository as upstream
git remote add upstream https://github.com/microgen-cli/microgen.git
```

### 2. Set Up Development Environment
```bash
# Install dependencies
npm install

# Build the project
npm run build

# Link for local development
npm link

# Verify installation
microgen --version
```

### 3. Make Your Changes
```bash
# Create a feature branch
git checkout -b feature/your-feature-name

# Make your changes
# ... edit files ...

# Test your changes
npm test
npm run lint

# Build and test the CLI
npm run build
microgen create test-project --dry-run
```

### 4. Submit Your Contribution
```bash
# Commit your changes
git add .
git commit -m "feat: add your feature description"

# Push to your fork
git push origin feature/your-feature-name

# Create a Pull Request on GitHub
```

## 📋 Types of Contributions

### 🐛 Bug Reports
Found a bug? Please create an issue with:
- Clear description of the problem
- Steps to reproduce
- Expected vs actual behavior
- Environment details (OS, Node.js version, CLI version)
- Error messages or logs

### ✨ Feature Requests
Have an idea for a new feature? Create an issue with:
- Clear description of the feature
- Use case and motivation
- Proposed implementation (if you have ideas)
- Examples of how it would work

### 📖 Documentation Improvements
Documentation can always be better! You can:
- Fix typos and grammar
- Add examples and clarifications
- Create new guides and tutorials
- Improve existing documentation

### 🔧 Code Contributions
We welcome code contributions for:
- Bug fixes
- New features
- Performance improvements
- Code quality improvements
- Test coverage improvements

## 🏗️ Development Setup

### Prerequisites
- Node.js 16.0 or higher
- npm 7.0 or higher
- Git

### Project Structure
```
microgen/
├── src/                    # Source code
│   ├── commands/          # CLI commands
│   ├── generators/        # Project generators
│   ├── types/            # TypeScript type definitions
│   └── utils/            # Utility functions
├── templates/             # Project templates
│   ├── frontend/         # Frontend templates
│   ├── nestjs/          # NestJS templates
│   ├── nextjs/          # Next.js templates
│   └── service/         # Service templates
├── dist/                 # Compiled JavaScript
├── tests/               # Test files
└── web/                 # Documentation site
```

### Development Commands
```bash
# Development mode (watch for changes)
npm run dev

# Build the project
npm run build

# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Type checking
npm run type-check
```

### Testing Your Changes
```bash
# Test the CLI locally
npm run build
npm link

# Test project generation
microgen create test-project --dry-run
microgen create test-project --template api-only

# Test in a temporary directory
cd /tmp
microgen create test-project
cd test-project
npm install
npm run build
```

## 📝 Coding Guidelines

### TypeScript Standards
- Use strict TypeScript configuration
- Provide proper type definitions
- Avoid `any` types when possible
- Use interfaces for object shapes
- Document complex types with JSDoc

### Code Style
- Use ESLint and Prettier configurations
- Follow existing code patterns
- Use meaningful variable and function names
- Keep functions small and focused
- Add comments for complex logic

### Example Code Style
```typescript
/**
 * Generates a new microservice project
 * @param config - Project configuration
 * @param options - Generation options
 * @returns Promise that resolves when generation is complete
 */
export async function generateProject(
  config: ProjectConfig,
  options: GeneratorOptions
): Promise<void> {
  try {
    this.logger.info(`Generating project: ${config.name}`);
    
    // Validate configuration
    this.validateConfig(config);
    
    // Generate project structure
    await this.createProjectStructure(config, options);
    
    this.logger.success('Project generated successfully!');
  } catch (error) {
    this.logger.error(`Failed to generate project: ${error.message}`);
    throw error;
  }
}
```

### Commit Message Format
We use conventional commits for clear commit history:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```bash
feat(templates): add Vue.js frontend template
fix(generator): resolve path resolution issue on Windows
docs(readme): update installation instructions
test(commands): add tests for create command options
```

## 🧪 Testing Guidelines

### Test Structure
```
src/
├── commands/
│   ├── create-project.ts
│   └── __tests__/
│       └── create-project.test.ts
├── generators/
│   ├── project-generator.ts
│   └── __tests__/
│       └── project-generator.test.ts
└── utils/
    ├── file-utils.ts
    └── __tests__/
        └── file-utils.test.ts
```

### Writing Tests
```typescript
import { CreateProjectCommand } from '../create-project';

describe('CreateProjectCommand', () => {
  let command: CreateProjectCommand;

  beforeEach(() => {
    command = new CreateProjectCommand();
  });

  describe('execute', () => {
    it('should create a project with valid configuration', async () => {
      const projectName = 'test-project';
      const options = {
        template: 'api-only',
        outputDir: './test-output',
        interactive: false,
      };

      await expect(command.execute(projectName, options)).resolves.not.toThrow();
    });

    it('should throw error for invalid project name', async () => {
      const projectName = 'invalid@name';
      const options = {};

      await expect(command.execute(projectName, options)).rejects.toThrow();
    });
  });
});
```

### Test Coverage
- Aim for at least 80% test coverage
- Test both success and error cases
- Include integration tests for CLI commands
- Test template generation with different configurations

## 📚 Documentation Guidelines

### Documentation Structure
- Keep documentation up to date with code changes
- Use clear, concise language
- Include code examples
- Add screenshots for UI-related features
- Use proper Markdown formatting

### Adding New Documentation
1. Create new `.md` files in `web/docs/`
2. Update `web/sidebars.ts` to include new pages
3. Test documentation locally:
   ```bash
   cd web
   npm start
   ```

## 🔄 Pull Request Process

### Before Submitting
- [ ] Code follows project style guidelines
- [ ] Tests pass (`npm test`)
- [ ] Linting passes (`npm run lint`)
- [ ] Documentation is updated
- [ ] Commit messages follow conventional format
- [ ] Branch is up to date with main

### Pull Request Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Other (please describe)

## Testing
- [ ] Tests pass
- [ ] New tests added (if applicable)
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)
```

### Review Process
1. Automated checks must pass
2. At least one maintainer review required
3. Address review feedback
4. Maintainer will merge when approved

## 🎯 Areas for Contribution

### High Priority
- [ ] Add support for more frontend frameworks
- [ ] Improve error handling and user experience
- [ ] Add more microservice transport options
- [ ] Enhance template customization
- [ ] Improve test coverage

### Medium Priority
- [ ] Add configuration file support
- [ ] Improve documentation with more examples
- [ ] Add plugin system for custom templates
- [ ] Performance optimizations
- [ ] Better Windows support

### Good First Issues
- [ ] Fix typos in documentation
- [ ] Add more unit tests
- [ ] Improve error messages
- [ ] Add new template examples
- [ ] Update dependencies

## 🤝 Community

### Getting Help
- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Documentation**: Check the docs first

### Code of Conduct
We follow the [Contributor Covenant Code of Conduct](https://www.contributor-covenant.org/). Please be respectful and inclusive in all interactions.

## 🏆 Recognition

Contributors will be:
- Listed in the project README
- Mentioned in release notes
- Invited to join the maintainer team (for significant contributions)

Thank you for contributing to Microservice CLI! 🎉
