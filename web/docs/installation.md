---
sidebar_position: 2
---

# Installation

Learn how to install and set up Microgen on your development environment.

## Prerequisites

Before installing Microgen, ensure you have the following prerequisites:

### Node.js
- **Node.js 16.0 or higher** is required
- **npm 7.0 or higher** (comes with Node.js)

Check your current versions:
```bash
node --version
npm --version
```

If you need to install or update Node.js, visit [nodejs.org](https://nodejs.org/) and download the latest LTS version.

### Git (Optional but Recommended)
Git is recommended for version control of your generated projects:
```bash
git --version
```

## Installation Methods

### Method 1: Global Installation (Recommended)

Install Microgen globally to use it from anywhere:

```bash
npm install -g microgen
```

After installation, verify it's working:
```bash
microgen --version
```

You can now use the CLI from any directory:
```bash
microgen create my-project
```

### Method 2: Using npx (No Installation Required)

Use npx to run Microgen without installing it globally:

```bash
npx microgen create my-project
```

This method:
- ✅ Always uses the latest version
- ✅ Doesn't require global installation
- ✅ Perfect for CI/CD environments
- ❌ Slightly slower (downloads each time)

### Method 3: Local Project Installation

Install as a development dependency in your project:

```bash
npm install --save-dev microgen
```

Then use it via npm scripts in your `package.json`:
```json
{
  "scripts": {
    "generate": "microgen create"
  }
}
```

Run with:
```bash
npm run generate my-project
```

## Verification

After installation, verify everything is working correctly:

### Check Version
```bash
microgen --version
```

### View Help
```bash
microgen --help
```

### Test with Dry Run
```bash
microgen create test-project --dry-run
```

This will show you what would be generated without actually creating files.

## Updating

### Update Global Installation
```bash
npm update -g microgen
```

### Check for Updates
```bash
npm outdated -g microgen
```

## Troubleshooting

### Permission Issues (macOS/Linux)

If you encounter permission errors during global installation:

```bash
# Option 1: Use sudo (not recommended)
sudo npm install -g microgen

# Option 2: Configure npm to use a different directory (recommended)
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
npm install -g microgen
```

### Windows Issues

If you encounter issues on Windows:

1. **Run as Administrator**: Open Command Prompt or PowerShell as Administrator
2. **Use PowerShell**: Use PowerShell instead of Command Prompt
3. **Check Execution Policy**: 
   ```powershell
   Get-ExecutionPolicy
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

### Common Issues

#### "Command not found" Error
- Ensure the installation completed successfully
- Check if the npm global bin directory is in your PATH
- Try restarting your terminal

#### Version Conflicts
- Uninstall and reinstall if you have version conflicts:
  ```bash
  npm uninstall -g microgen
  npm install -g microgen
  ```

#### Network Issues
- If installation fails due to network issues, try:
  ```bash
  npm install -g microgen --registry https://registry.npmjs.org/
  ```

## Next Steps

Once you have Microgen installed:

1. **[Quick Start Guide](./quick-start)** - Create your first project
2. **[CLI Commands](./commands/create)** - Learn about available commands
3. **[Project Templates](./templates/overview)** - Explore different project types

## Alternative Package Managers

### Using Yarn
```bash
# Global installation
yarn global add microgen

# Using yarn dlx (like npx)
yarn dlx microgen create my-project
```

### Using pnpm
```bash
# Global installation
pnpm add -g microgen

# Using pnpm dlx (like npx)
pnpm dlx microgen create my-project
```

## Development Installation

If you want to contribute to Microgen or run the latest development version:

```bash
# Clone the repository
git clone https://github.com/microgen-cli/microgen.git
cd microgen

# Install dependencies
npm install

# Build the project
npm run build

# Link for local development
npm link

# Now you can use the development version
microgen create test-project
```
