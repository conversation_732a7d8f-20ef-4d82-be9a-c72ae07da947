---
sidebar_position: 4
---

# Scaling Strategies

Learn how to scale your Microgen-generated applications to handle increased load and traffic.

## Overview

Scaling is essential for growing applications. This guide covers horizontal and vertical scaling strategies for applications built with Microgen.

## Horizontal Scaling

### Load Balancing

**NGINX Load Balancer:**
```nginx
upstream backend {
    server backend1:3000;
    server backend2:3000;
    server backend3:3000;
}

server {
    listen 80;
    
    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

**Docker Compose Scaling:**
```yaml
version: '3.8'
services:
  backend:
    build: ./backend
    deploy:
      replicas: 3
    environment:
      - NODE_ENV=production
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - backend
```

### Container Orchestration

**Kubernetes Deployment:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-deployment
spec:
  replicas: 5
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: myapp/backend:latest
        ports:
        - containerPort: 3000
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

**Horizontal Pod Autoscaler:**
```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-deployment
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

## Vertical Scaling

### Resource Optimization

**Memory Management:**
```typescript
// Memory monitoring
process.on('warning', (warning) => {
  console.warn(warning.name);
  console.warn(warning.message);
  console.warn(warning.stack);
});

// Garbage collection monitoring
if (global.gc) {
  setInterval(() => {
    const memBefore = process.memoryUsage();
    global.gc();
    const memAfter = process.memoryUsage();
    console.log('GC freed:', memBefore.heapUsed - memAfter.heapUsed);
  }, 60000);
}
```

**CPU Optimization:**
```typescript
// Worker threads for CPU-intensive tasks
import { Worker, isMainThread, parentPort } from 'worker_threads';

if (isMainThread) {
  // Main thread
  const worker = new Worker(__filename);
  worker.postMessage({ data: heavyComputationData });
  worker.on('message', (result) => {
    console.log('Result:', result);
  });
} else {
  // Worker thread
  parentPort.on('message', ({ data }) => {
    const result = performHeavyComputation(data);
    parentPort.postMessage(result);
  });
}
```

## Database Scaling

### Read Replicas

```typescript
// Database configuration with read replicas
const config: TypeOrmModuleOptions = {
  type: 'postgres',
  replication: {
    master: {
      host: 'master-db',
      port: 5432,
      username: 'user',
      password: 'password',
      database: 'myapp',
    },
    slaves: [
      {
        host: 'slave-db-1',
        port: 5432,
        username: 'user',
        password: 'password',
        database: 'myapp',
      },
      {
        host: 'slave-db-2',
        port: 5432,
        username: 'user',
        password: 'password',
        database: 'myapp',
      },
    ],
  },
};
```

### Database Sharding

```typescript
// Simple sharding strategy
@Injectable()
export class ShardingService {
  private getShardKey(userId: string): string {
    const hash = crypto.createHash('md5').update(userId).digest('hex');
    const shardNumber = parseInt(hash.substring(0, 2), 16) % 4;
    return `shard_${shardNumber}`;
  }

  async getUserData(userId: string) {
    const shard = this.getShardKey(userId);
    const connection = this.getConnection(shard);
    return connection.query('SELECT * FROM users WHERE id = ?', [userId]);
  }
}
```

## Caching Strategies

### Application-Level Caching

```typescript
// Redis caching
@Injectable()
export class CacheService {
  constructor(private redis: Redis) {}

  async get<T>(key: string): Promise<T | null> {
    const cached = await this.redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }

  async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    await this.redis.setex(key, ttl, JSON.stringify(value));
  }

  async invalidate(pattern: string): Promise<void> {
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}
```

### CDN Integration

```typescript
// CloudFront configuration
const cloudFrontConfig = {
  origins: [
    {
      domainName: 'api.myapp.com',
      originPath: '/api',
      customOriginConfig: {
        httpPort: 80,
        httpsPort: 443,
        originProtocolPolicy: 'https-only',
      },
    },
  ],
  defaultCacheBehavior: {
    targetOriginId: 'api-origin',
    viewerProtocolPolicy: 'redirect-to-https',
    cachePolicyId: 'managed-caching-optimized',
  },
};
```

## Message Queue Scaling

### Kafka Scaling

```yaml
# Kafka cluster
version: '3.8'
services:
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181

  kafka-1:
    image: confluentinc/cp-kafka:latest
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-1:9092

  kafka-2:
    image: confluentinc/cp-kafka:latest
    environment:
      KAFKA_BROKER_ID: 2
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-2:9092

  kafka-3:
    image: confluentinc/cp-kafka:latest
    environment:
      KAFKA_BROKER_ID: 3
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-3:9092
```

### Consumer Scaling

```typescript
// Kafka consumer scaling
@Injectable()
export class ScalableConsumer {
  private consumers: Consumer[] = [];

  async startConsumers(partitionCount: number) {
    for (let i = 0; i < partitionCount; i++) {
      const consumer = this.kafka.consumer({
        groupId: `consumer-group-${i}`,
      });

      await consumer.subscribe({ topic: 'events' });
      
      await consumer.run({
        eachMessage: async ({ message, partition }) => {
          await this.processMessage(message, partition);
        },
      });

      this.consumers.push(consumer);
    }
  }
}
```

## Monitoring Scaling

### Metrics Collection

```typescript
// Custom metrics for scaling decisions
@Injectable()
export class ScalingMetrics {
  private promClient = require('prom-client');
  
  private requestDuration = new this.promClient.Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route', 'status'],
  });

  private activeConnections = new this.promClient.Gauge({
    name: 'active_connections',
    help: 'Number of active connections',
  });

  recordRequest(method: string, route: string, status: number, duration: number) {
    this.requestDuration.labels(method, route, status.toString()).observe(duration);
  }

  setActiveConnections(count: number) {
    this.activeConnections.set(count);
  }
}
```

### Auto-scaling Rules

```yaml
# Kubernetes VPA (Vertical Pod Autoscaler)
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: backend-vpa
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-deployment
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: backend
      maxAllowed:
        cpu: 1
        memory: 2Gi
      minAllowed:
        cpu: 100m
        memory: 128Mi
```

## Performance Testing

### Load Testing

```javascript
// Artillery load test
module.exports = {
  config: {
    target: 'http://localhost:3000',
    phases: [
      { duration: 60, arrivalRate: 10 },
      { duration: 120, arrivalRate: 50 },
      { duration: 60, arrivalRate: 100 },
    ],
  },
  scenarios: [
    {
      name: 'API Load Test',
      requests: [
        {
          get: {
            url: '/api/users',
          },
        },
        {
          post: {
            url: '/api/users',
            json: {
              name: 'Test User',
              email: '<EMAIL>',
            },
          },
        },
      ],
    },
  ],
};
```

### Stress Testing

```bash
# K6 stress test
import http from 'k6/http';
import { check } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 },
    { duration: '5m', target: 100 },
    { duration: '2m', target: 200 },
    { duration: '5m', target: 200 },
    { duration: '2m', target: 0 },
  ],
};

export default function() {
  let response = http.get('http://localhost:3000/api/health');
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });
}
```

## Cost Optimization

### Resource Right-sizing

```typescript
// Resource monitoring
@Injectable()
export class ResourceMonitor {
  @Cron('0 */6 * * *') // Every 6 hours
  async analyzeResourceUsage() {
    const metrics = await this.getResourceMetrics();
    
    if (metrics.cpuUsage < 30 && metrics.memoryUsage < 40) {
      this.logger.warn('Resources may be over-provisioned');
    }
    
    if (metrics.cpuUsage > 80 || metrics.memoryUsage > 85) {
      this.logger.error('Resources may be under-provisioned');
    }
  }
}
```

## Coming Soon

This documentation section is under development. Future additions will include:

- Advanced auto-scaling strategies
- Multi-region deployment
- Edge computing integration
- Cost optimization techniques

## Next Steps

- [**Monitoring Setup**](./monitoring) - Monitor your scaled applications
- [**Security Hardening**](./security) - Secure at scale
- [**Backup and Recovery**](./backup) - Protect scaled systems
