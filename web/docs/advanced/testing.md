---
sidebar_position: 5
---

# Testing Templates

Learn how to test your Microgen templates to ensure they work correctly and generate valid projects.

## Overview

Testing templates is crucial for maintaining quality and ensuring that generated projects work as expected. Microgen provides testing utilities and best practices for comprehensive template testing.

## Testing Framework

### Installation

```bash
npm install --save-dev @microgen/testing
```

### Basic Test Setup

```javascript
// test/template.test.js
const { generateProject, validateProject } = require('@microgen/testing');
const path = require('path');

describe('My Template', () => {
  const templatePath = path.join(__dirname, '..');
  const outputDir = path.join(__dirname, 'output');
  
  beforeEach(async () => {
    await fs.ensureDir(outputDir);
  });
  
  afterEach(async () => {
    await fs.remove(outputDir);
  });
  
  test('should generate basic project', async () => {
    const answers = {
      projectName: 'test-project',
      framework: 'nestjs'
    };
    
    const result = await generateProject(templatePath, answers, outputDir);
    
    expect(result.success).toBe(true);
    expect(result.files).toContain('package.json');
  });
});
```

## Test Types

### Unit Tests
Test individual template components and helpers.

### Integration Tests
Test complete project generation with various configurations.

### End-to-End Tests
Test generated projects by building and running them.

## Coming Soon

This documentation section is under development. Check back soon for complete testing guidelines and examples.

## Next Steps

- [**Template API**](./template-api) - Learn the template API
- [**Custom Templates**](./custom-templates) - Create your own templates
- [**Contributing**](./contributing-templates) - Contribute to the community
