---
sidebar_position: 1
---

# Custom Templates

<PERSON><PERSON> how to create and use custom templates with Microgen to standardize your team's project structure and configurations.

## Overview

Custom templates allow you to:
- Standardize project structure across your organization
- Include company-specific configurations
- Add custom boilerplate code
- Integrate with internal tools and services
- Enforce coding standards and best practices

## Template Structure

A Microgen template is a directory containing:

```
my-custom-template/
├── template.json          # Template configuration
├── files/                 # Template files and directories
│   ├── backend/
│   │   ├── src/
│   │   ├── package.json.hbs
│   │   └── Dockerfile.hbs
│   ├── frontend/
│   │   ├── src/
│   │   ├── package.json.hbs
│   │   └── next.config.js.hbs
│   └── docker-compose.yml.hbs
├── hooks/                 # Template hooks (optional)
│   ├── pre-generate.js
│   └── post-generate.js
└── README.md             # Template documentation
```

## Template Configuration

### template.json

```json
{
  "name": "company-fullstack",
  "version": "1.0.0",
  "description": "Company standard full-stack template",
  "author": "Your Company <<EMAIL>>",
  "type": "full",
  "tags": ["fullstack", "company", "standard"],
  
  "prompts": [
    {
      "name": "projectName",
      "type": "input",
      "message": "Project name:",
      "validate": "required"
    },
    {
      "name": "description",
      "type": "input",
      "message": "Project description:",
      "default": "A new project"
    },
    {
      "name": "backendFramework",
      "type": "select",
      "message": "Choose backend framework:",
      "choices": [
        { "name": "NestJS", "value": "nestjs" },
        { "name": "Express", "value": "express" }
      ],
      "default": "nestjs"
    },
    {
      "name": "database",
      "type": "select",
      "message": "Choose database:",
      "choices": [
        { "name": "PostgreSQL", "value": "postgresql" },
        { "name": "MySQL", "value": "mysql" },
        { "name": "MongoDB", "value": "mongodb" }
      ],
      "default": "postgresql"
    },
    {
      "name": "frontendFramework",
      "type": "select",
      "message": "Choose frontend framework:",
      "choices": [
        { "name": "Next.js", "value": "nextjs" },
        { "name": "React", "value": "react" },
        { "name": "Vue.js", "value": "vue" }
      ],
      "default": "nextjs"
    },
    {
      "name": "styling",
      "type": "select",
      "message": "Choose styling solution:",
      "choices": [
        { "name": "Tailwind CSS", "value": "tailwind" },
        { "name": "Styled Components", "value": "styled-components" },
        { "name": "CSS Modules", "value": "css-modules" }
      ],
      "default": "tailwind"
    },
    {
      "name": "authentication",
      "type": "confirm",
      "message": "Include authentication?",
      "default": true
    },
    {
      "name": "testing",
      "type": "confirm",
      "message": "Include testing setup?",
      "default": true
    },
    {
      "name": "docker",
      "type": "confirm",
      "message": "Include Docker configuration?",
      "default": true
    }
  ],
  
  "conditions": [
    {
      "when": "backendFramework === 'nestjs'",
      "include": ["backend/nestjs/**/*"]
    },
    {
      "when": "backendFramework === 'express'",
      "include": ["backend/express/**/*"]
    },
    {
      "when": "frontendFramework === 'nextjs'",
      "include": ["frontend/nextjs/**/*"]
    },
    {
      "when": "frontendFramework === 'react'",
      "include": ["frontend/react/**/*"]
    },
    {
      "when": "authentication === true",
      "include": ["auth/**/*"]
    },
    {
      "when": "docker === true",
      "include": ["docker/**/*"]
    }
  ],
  
  "dependencies": {
    "backend": {
      "nestjs": {
        "dependencies": [
          "@nestjs/core",
          "@nestjs/common",
          "@nestjs/platform-express"
        ],
        "devDependencies": [
          "@nestjs/cli",
          "@nestjs/testing"
        ]
      },
      "express": {
        "dependencies": [
          "express",
          "cors",
          "helmet"
        ],
        "devDependencies": [
          "@types/express",
          "nodemon"
        ]
      }
    },
    "frontend": {
      "nextjs": {
        "dependencies": [
          "next",
          "react",
          "react-dom"
        ],
        "devDependencies": [
          "@types/react",
          "@types/node",
          "typescript"
        ]
      }
    }
  },
  
  "scripts": {
    "backend": {
      "dev": "npm run start:dev",
      "build": "npm run build",
      "start": "npm run start:prod",
      "test": "npm run test"
    },
    "frontend": {
      "dev": "next dev",
      "build": "next build",
      "start": "next start",
      "test": "jest"
    }
  }
}
```

## Template Files

### Using Handlebars Templates

Template files use Handlebars syntax for dynamic content:

```json
// files/backend/package.json.hbs
{
  "name": "{{projectName}}-backend",
  "version": "1.0.0",
  "description": "{{description}} - Backend API",
  "main": "dist/main.js",
  "scripts": {
    {{#each scripts.backend}}
    "{{@key}}": "{{this}}",
    {{/each}}
  },
  "dependencies": {
    {{#each dependencies.backend.[backendFramework].dependencies}}
    "{{this}}": "latest",
    {{/each}}
    {{#if authentication}}
    "@nestjs/jwt": "latest",
    "@nestjs/passport": "latest",
    "passport-jwt": "latest",
    {{/if}}
    {{#if (eq database 'postgresql')}}
    "@nestjs/typeorm": "latest",
    "typeorm": "latest",
    "pg": "latest",
    {{/if}}
  },
  "devDependencies": {
    {{#each dependencies.backend.[backendFramework].devDependencies}}
    "{{this}}": "latest",
    {{/each}}
    {{#if testing}}
    "jest": "latest",
    "@types/jest": "latest",
    {{/if}}
  }
}
```

### Conditional File Inclusion

```typescript
// files/backend/src/auth/auth.module.ts.hbs
{{#if authentication}}
import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtStrategy } from './jwt.strategy';

@Module({
  imports: [
    PassportModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your-secret-key',
      signOptions: { expiresIn: '24h' },
    }),
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy],
  exports: [AuthService],
})
export class AuthModule {}
{{/if}}
```

## Template Hooks

### Pre-generation Hook

```javascript
// hooks/pre-generate.js
module.exports = async (context) => {
  const { answers, utils } = context;
  
  // Validate project name
  if (!utils.isValidPackageName(answers.projectName)) {
    throw new Error('Invalid project name for npm package');
  }
  
  // Set derived values
  answers.projectNameKebab = utils.kebabCase(answers.projectName);
  answers.projectNamePascal = utils.pascalCase(answers.projectName);
  
  // Check for existing directory
  if (utils.directoryExists(answers.projectName)) {
    const overwrite = await utils.confirm(
      `Directory ${answers.projectName} already exists. Overwrite?`
    );
    if (!overwrite) {
      process.exit(0);
    }
  }
  
  console.log(`Generating ${answers.projectName} with ${answers.backendFramework} + ${answers.frontendFramework}`);
};
```

### Post-generation Hook

```javascript
// hooks/post-generate.js
const { execSync } = require('child_process');
const path = require('path');

module.exports = async (context) => {
  const { answers, outputDir, utils } = context;
  
  console.log('Installing dependencies...');
  
  // Install backend dependencies
  const backendDir = path.join(outputDir, 'backend');
  if (utils.directoryExists(backendDir)) {
    process.chdir(backendDir);
    execSync('npm install', { stdio: 'inherit' });
  }
  
  // Install frontend dependencies
  const frontendDir = path.join(outputDir, 'frontend');
  if (utils.directoryExists(frontendDir)) {
    process.chdir(frontendDir);
    execSync('npm install', { stdio: 'inherit' });
  }
  
  // Initialize git repository
  process.chdir(outputDir);
  execSync('git init', { stdio: 'inherit' });
  execSync('git add .', { stdio: 'inherit' });
  execSync('git commit -m "Initial commit"', { stdio: 'inherit' });
  
  // Generate environment files
  utils.copyTemplate(
    path.join(__dirname, '../templates/env.example'),
    path.join(outputDir, '.env.example'),
    answers
  );
  
  console.log('\n✅ Project generated successfully!');
  console.log('\nNext steps:');
  console.log(`  cd ${answers.projectName}`);
  console.log('  docker-compose up -d');
  console.log('  npm run dev');
};
```

## Using Custom Templates

### Local Templates

```bash
# Use local template directory
microgen create my-project --template-dir ./my-custom-template

# Use specific template from directory
microgen create my-project --template ./templates/company-fullstack
```

### Remote Templates

```bash
# Use template from Git repository
microgen create my-project --template git+https://github.com/company/microgen-templates.git#company-fullstack

# Use template from npm package
microgen create my-project --template @company/microgen-template-fullstack
```

### Template Registry

```bash
# Add template registry
microgen config set registry https://templates.company.com

# List available templates
microgen templates list

# Use template from registry
microgen create my-project --template company-fullstack
```

## Template Development

### Testing Templates

```bash
# Test template generation
microgen create test-project --template ./my-template --dry-run

# Validate template structure
microgen template validate ./my-template

# Test with different answers
microgen create test-project --template ./my-template --answers ./test-answers.json
```

### Template Validation

```json
// test-answers.json
{
  "projectName": "test-project",
  "description": "Test project for template validation",
  "backendFramework": "nestjs",
  "frontendFramework": "nextjs",
  "database": "postgresql",
  "authentication": true,
  "testing": true,
  "docker": true
}
```

### Publishing Templates

```bash
# Package template
microgen template package ./my-template

# Publish to npm
npm publish microgen-template-company-fullstack

# Publish to private registry
npm publish --registry https://npm.company.com
```

## Best Practices

### Template Organization
- Use clear, descriptive template names
- Include comprehensive documentation
- Version your templates
- Test templates thoroughly

### File Structure
- Group related files in directories
- Use consistent naming conventions
- Include example configurations
- Provide sensible defaults

### Handlebars Usage
- Use helpers for complex logic
- Keep templates readable
- Validate required variables
- Provide fallback values

### Hooks
- Keep hooks simple and focused
- Handle errors gracefully
- Provide user feedback
- Test hook functionality

## Advanced Features

### Custom Helpers

```javascript
// helpers/string-helpers.js
module.exports = {
  uppercase: (str) => str.toUpperCase(),
  lowercase: (str) => str.toLowerCase(),
  camelCase: (str) => str.replace(/-([a-z])/g, (g) => g[1].toUpperCase()),
  kebabCase: (str) => str.replace(/([A-Z])/g, '-$1').toLowerCase(),
  pascalCase: (str) => str.charAt(0).toUpperCase() + str.slice(1),
  
  // Conditional helpers
  eq: (a, b) => a === b,
  ne: (a, b) => a !== b,
  gt: (a, b) => a > b,
  lt: (a, b) => a < b,
  
  // Array helpers
  join: (arr, separator = ', ') => arr.join(separator),
  first: (arr) => arr[0],
  last: (arr) => arr[arr.length - 1],
};
```

### Dynamic Dependencies

```javascript
// hooks/dependencies.js
module.exports = (answers) => {
  const dependencies = {};
  
  // Base dependencies
  if (answers.backendFramework === 'nestjs') {
    dependencies['@nestjs/core'] = '^10.0.0';
    dependencies['@nestjs/common'] = '^10.0.0';
  }
  
  // Conditional dependencies
  if (answers.authentication) {
    dependencies['@nestjs/jwt'] = '^10.0.0';
    dependencies['@nestjs/passport'] = '^10.0.0';
  }
  
  if (answers.database === 'postgresql') {
    dependencies['@nestjs/typeorm'] = '^10.0.0';
    dependencies['typeorm'] = '^0.3.0';
    dependencies['pg'] = '^8.0.0';
  }
  
  return dependencies;
};
```

## Next Steps

- [**Template Examples**](./template-examples) - Ready-to-use template examples
- [**Template API Reference**](./template-api) - Complete API documentation
- [**Contributing Templates**](./contributing-templates) - Share your templates
- [**Configuration Guide**](../configuration/project-config) - Project configuration options
