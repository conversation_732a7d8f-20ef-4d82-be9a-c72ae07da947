---
sidebar_position: 2
---

# Template Examples

Ready-to-use template examples for common project types and organizational standards.

## Basic Templates

### Simple API Template

A minimal API template for quick prototyping:

```json
{
  "name": "simple-api",
  "version": "1.0.0",
  "description": "Simple REST API template",
  "type": "api-only",
  "prompts": [
    {
      "name": "projectName",
      "type": "input",
      "message": "Project name:",
      "validate": "required"
    },
    {
      "name": "framework",
      "type": "select",
      "message": "Choose framework:",
      "choices": [
        { "name": "Express.js", "value": "express" },
        { "name": "Fastify", "value": "fastify" }
      ],
      "default": "express"
    }
  ]
}
```

### React SPA Template

Single-page application template:

```json
{
  "name": "react-spa",
  "version": "1.0.0",
  "description": "React Single Page Application",
  "type": "frontend-only",
  "prompts": [
    {
      "name": "projectN<PERSON>",
      "type": "input",
      "message": "Project name:",
      "validate": "required"
    },
    {
      "name": "styling",
      "type": "select",
      "message": "Choose styling:",
      "choices": [
        { "name": "Tailwind CSS", "value": "tailwind" },
        { "name": "Styled Components", "value": "styled-components" },
        { "name": "Material-UI", "value": "mui" }
      ]
    },
    {
      "name": "router",
      "type": "confirm",
      "message": "Include React Router?",
      "default": true
    }
  ]
}
```

## Enterprise Templates

### Microservice Template

Complete microservice with observability:

```json
{
  "name": "enterprise-microservice",
  "version": "1.0.0",
  "description": "Enterprise-grade microservice template",
  "type": "api-only",
  "prompts": [
    {
      "name": "serviceName",
      "type": "input",
      "message": "Service name:",
      "validate": "required"
    },
    {
      "name": "domain",
      "type": "input",
      "message": "Business domain:",
      "validate": "required"
    },
    {
      "name": "database",
      "type": "select",
      "message": "Database:",
      "choices": [
        { "name": "PostgreSQL", "value": "postgresql" },
        { "name": "MongoDB", "value": "mongodb" }
      ]
    },
    {
      "name": "messaging",
      "type": "multiselect",
      "message": "Messaging patterns:",
      "choices": [
        { "name": "Apache Kafka", "value": "kafka" },
        { "name": "RabbitMQ", "value": "rabbitmq" },
        { "name": "Redis Pub/Sub", "value": "redis" }
      ]
    },
    {
      "name": "observability",
      "type": "confirm",
      "message": "Include observability stack?",
      "default": true
    }
  ],
  "conditions": [
    {
      "when": "observability === true",
      "include": [
        "observability/**/*",
        "monitoring/**/*"
      ]
    }
  ]
}
```

### Full-Stack Enterprise Template

```json
{
  "name": "enterprise-fullstack",
  "version": "1.0.0",
  "description": "Enterprise full-stack application",
  "type": "full",
  "prompts": [
    {
      "name": "projectName",
      "type": "input",
      "message": "Project name:",
      "validate": "required"
    },
    {
      "name": "organization",
      "type": "input",
      "message": "Organization name:",
      "validate": "required"
    },
    {
      "name": "architecture",
      "type": "select",
      "message": "Architecture pattern:",
      "choices": [
        { "name": "Monolithic", "value": "monolith" },
        { "name": "Microservices", "value": "microservices" },
        { "name": "Modular Monolith", "value": "modular-monolith" }
      ]
    },
    {
      "name": "authentication",
      "type": "select",
      "message": "Authentication method:",
      "choices": [
        { "name": "JWT", "value": "jwt" },
        { "name": "OAuth 2.0", "value": "oauth2" },
        { "name": "SAML", "value": "saml" }
      ]
    },
    {
      "name": "deployment",
      "type": "select",
      "message": "Deployment target:",
      "choices": [
        { "name": "Docker Compose", "value": "docker-compose" },
        { "name": "Kubernetes", "value": "kubernetes" },
        { "name": "AWS ECS", "value": "aws-ecs" }
      ]
    }
  ]
}
```

## Framework-Specific Templates

### NestJS Microservice

```typescript
// hooks/post-generate.js
module.exports = async (context) => {
  const { answers, outputDir, utils } = context;
  
  // Generate OpenAPI specification
  if (answers.generateDocs) {
    await utils.generateFile(
      'swagger.yaml',
      generateSwaggerSpec(answers)
    );
  }
  
  // Setup database migrations
  if (answers.database === 'postgresql') {
    await utils.runCommand('npm run migration:generate -- InitialMigration');
  }
  
  // Configure monitoring
  if (answers.monitoring) {
    await utils.copyTemplate(
      'monitoring/prometheus.yml',
      'monitoring/prometheus.yml',
      answers
    );
  }
};

function generateSwaggerSpec(answers) {
  return `
openapi: 3.0.0
info:
  title: ${answers.serviceName} API
  version: 1.0.0
  description: ${answers.description}
servers:
  - url: http://localhost:3000
    description: Development server
paths:
  /health:
    get:
      summary: Health check
      responses:
        '200':
          description: Service is healthy
`;
}
```

### Next.js Application

```json
{
  "name": "nextjs-app",
  "version": "1.0.0",
  "description": "Next.js application template",
  "type": "frontend-only",
  "prompts": [
    {
      "name": "projectName",
      "type": "input",
      "message": "Project name:",
      "validate": "required"
    },
    {
      "name": "features",
      "type": "multiselect",
      "message": "Select features:",
      "choices": [
        { "name": "TypeScript", "value": "typescript", "checked": true },
        { "name": "Tailwind CSS", "value": "tailwind", "checked": true },
        { "name": "ESLint", "value": "eslint", "checked": true },
        { "name": "Prettier", "value": "prettier", "checked": true },
        { "name": "Husky", "value": "husky" },
        { "name": "Storybook", "value": "storybook" },
        { "name": "Jest", "value": "jest" },
        { "name": "Cypress", "value": "cypress" }
      ]
    },
    {
      "name": "deployment",
      "type": "select",
      "message": "Deployment platform:",
      "choices": [
        { "name": "Vercel", "value": "vercel" },
        { "name": "Netlify", "value": "netlify" },
        { "name": "Docker", "value": "docker" }
      ]
    }
  ]
}
```

## Industry-Specific Templates

### E-commerce Template

```json
{
  "name": "ecommerce-platform",
  "version": "1.0.0",
  "description": "E-commerce platform template",
  "type": "full",
  "prompts": [
    {
      "name": "projectName",
      "type": "input",
      "message": "Store name:",
      "validate": "required"
    },
    {
      "name": "features",
      "type": "multiselect",
      "message": "E-commerce features:",
      "choices": [
        { "name": "Product Catalog", "value": "catalog", "checked": true },
        { "name": "Shopping Cart", "value": "cart", "checked": true },
        { "name": "Payment Processing", "value": "payments", "checked": true },
        { "name": "Order Management", "value": "orders", "checked": true },
        { "name": "Inventory Management", "value": "inventory" },
        { "name": "Customer Reviews", "value": "reviews" },
        { "name": "Wishlist", "value": "wishlist" },
        { "name": "Recommendations", "value": "recommendations" }
      ]
    },
    {
      "name": "paymentProviders",
      "type": "multiselect",
      "message": "Payment providers:",
      "choices": [
        { "name": "Stripe", "value": "stripe" },
        { "name": "PayPal", "value": "paypal" },
        { "name": "Square", "value": "square" }
      ]
    }
  ]
}
```

### SaaS Template

```json
{
  "name": "saas-platform",
  "version": "1.0.0",
  "description": "SaaS platform template",
  "type": "full",
  "prompts": [
    {
      "name": "projectName",
      "type": "input",
      "message": "SaaS name:",
      "validate": "required"
    },
    {
      "name": "subscriptionModel",
      "type": "select",
      "message": "Subscription model:",
      "choices": [
        { "name": "Freemium", "value": "freemium" },
        { "name": "Tiered Pricing", "value": "tiered" },
        { "name": "Usage-based", "value": "usage" }
      ]
    },
    {
      "name": "features",
      "type": "multiselect",
      "message": "SaaS features:",
      "choices": [
        { "name": "Multi-tenancy", "value": "multitenancy", "checked": true },
        { "name": "Billing & Invoicing", "value": "billing", "checked": true },
        { "name": "User Management", "value": "users", "checked": true },
        { "name": "Analytics Dashboard", "value": "analytics" },
        { "name": "API Management", "value": "api" },
        { "name": "Webhooks", "value": "webhooks" }
      ]
    }
  ]
}
```

## Team Templates

### Startup Template

```json
{
  "name": "startup-mvp",
  "version": "1.0.0",
  "description": "Startup MVP template",
  "type": "full",
  "prompts": [
    {
      "name": "projectName",
      "type": "input",
      "message": "Startup name:",
      "validate": "required"
    },
    {
      "name": "mvpType",
      "type": "select",
      "message": "MVP type:",
      "choices": [
        { "name": "Landing Page + Waitlist", "value": "landing" },
        { "name": "Simple CRUD App", "value": "crud" },
        { "name": "Marketplace", "value": "marketplace" },
        { "name": "Social Platform", "value": "social" }
      ]
    },
    {
      "name": "analytics",
      "type": "multiselect",
      "message": "Analytics tools:",
      "choices": [
        { "name": "Google Analytics", "value": "ga" },
        { "name": "Mixpanel", "value": "mixpanel" },
        { "name": "Amplitude", "value": "amplitude" }
      ]
    }
  ]
}
```

### Agency Template

```json
{
  "name": "agency-project",
  "version": "1.0.0",
  "description": "Agency client project template",
  "type": "full",
  "prompts": [
    {
      "name": "clientName",
      "type": "input",
      "message": "Client name:",
      "validate": "required"
    },
    {
      "name": "projectType",
      "type": "select",
      "message": "Project type:",
      "choices": [
        { "name": "Corporate Website", "value": "corporate" },
        { "name": "E-commerce", "value": "ecommerce" },
        { "name": "Web Application", "value": "webapp" },
        { "name": "Mobile App Backend", "value": "mobile-backend" }
      ]
    },
    {
      "name": "cms",
      "type": "select",
      "message": "Content management:",
      "choices": [
        { "name": "Headless CMS", "value": "headless" },
        { "name": "Traditional CMS", "value": "traditional" },
        { "name": "Static Content", "value": "static" }
      ]
    }
  ]
}
```

## Custom Helpers

### Template Helpers

```javascript
// helpers/project-helpers.js
module.exports = {
  // String manipulation
  slugify: (str) => str.toLowerCase().replace(/[^a-z0-9]/g, '-'),
  capitalize: (str) => str.charAt(0).toUpperCase() + str.slice(1),
  
  // Project structure
  generateServiceName: (domain, service) => `${domain}-${service}-service`,
  generateApiPath: (version, resource) => `/api/v${version}/${resource}`,
  
  // Configuration
  generatePort: (basePort, index) => basePort + index,
  generateDatabaseUrl: (type, name) => {
    const urls = {
      postgresql: `postgresql://user:pass@localhost:5432/${name}`,
      mongodb: `mongodb://localhost:27017/${name}`,
      mysql: `mysql://user:pass@localhost:3306/${name}`
    };
    return urls[type];
  },
  
  // Validation
  isValidServiceName: (name) => /^[a-z][a-z0-9-]*[a-z0-9]$/.test(name),
  isValidPort: (port) => port >= 1024 && port <= 65535,
  
  // File operations
  shouldIncludeFile: (conditions, answers) => {
    return conditions.every(condition => {
      return eval(condition.replace(/(\w+)/g, 'answers.$1'));
    });
  }
};
```

### Advanced Conditions

```json
{
  "conditions": [
    {
      "when": "architecture === 'microservices' && messaging.includes('kafka')",
      "include": ["kafka/**/*", "event-sourcing/**/*"]
    },
    {
      "when": "deployment === 'kubernetes' && monitoring === true",
      "include": ["k8s/monitoring/**/*"]
    },
    {
      "when": "authentication === 'oauth2' && features.includes('social-login')",
      "include": ["auth/oauth/**/*", "auth/social/**/*"]
    }
  ]
}
```

## Template Testing

### Test Configuration

```json
{
  "tests": [
    {
      "name": "Basic API generation",
      "answers": {
        "projectName": "test-api",
        "framework": "express",
        "database": "postgresql"
      },
      "expectations": [
        "package.json exists",
        "src/app.js exists",
        "docker-compose.yml exists"
      ]
    },
    {
      "name": "Full-stack generation",
      "answers": {
        "projectName": "test-fullstack",
        "backend": "nestjs",
        "frontend": "nextjs",
        "database": "postgresql"
      },
      "expectations": [
        "backend/package.json exists",
        "frontend/package.json exists",
        "docker-compose.yml exists"
      ]
    }
  ]
}
```

### Automated Testing

```javascript
// test/template.test.js
const { generateProject, validateProject } = require('../lib/generator');

describe('Template Generation', () => {
  test('should generate basic API project', async () => {
    const result = await generateProject('simple-api', {
      projectName: 'test-api',
      framework: 'express'
    });
    
    expect(result.files).toContain('package.json');
    expect(result.files).toContain('src/app.js');
    expect(result.files).toContain('Dockerfile');
  });
  
  test('should validate generated project', async () => {
    const validation = await validateProject('./test-output/test-api');
    
    expect(validation.valid).toBe(true);
    expect(validation.errors).toHaveLength(0);
  });
});
```

## Next Steps

- [**Template API Reference**](./template-api) - Complete API documentation
- [**Contributing Templates**](./contributing-templates) - Share your templates
- [**Custom Templates Guide**](./custom-templates) - Create your own templates
- [**Template Registry**](./template-registry) - Browse available templates
