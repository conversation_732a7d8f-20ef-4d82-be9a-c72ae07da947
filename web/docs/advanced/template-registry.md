---
sidebar_position: 7
---

# Template Registry

Browse and discover templates from the Microgen community.

## Official Templates

### Basic Templates

**simple-api**
- Express.js REST API
- PostgreSQL database
- Docker configuration
- Basic authentication

**react-spa**
- React single-page application
- Tailwind CSS styling
- React Router
- Modern build setup

**nextjs-app**
- Next.js application
- TypeScript support
- Tailwind CSS
- API routes

### Full-Stack Templates

**fullstack-basic**
- NestJS backend
- Next.js frontend
- PostgreSQL database
- JWT authentication

**microservice-platform**
- Multiple microservices
- Kafka messaging
- Docker Compose
- API Gateway

## Community Templates

### Framework-Specific

**nestjs-microservice**
- Advanced NestJS setup
- gRPC communication
- Event sourcing
- Monitoring

**vue-pwa**
- Vue.js Progressive Web App
- Service workers
- Offline support
- Push notifications

### Industry-Specific

**ecommerce-platform**
- Complete e-commerce solution
- Payment integration
- Inventory management
- Admin dashboard

**saas-starter**
- Multi-tenant SaaS
- Subscription billing
- User management
- Analytics

## Using Templates

### From Registry

```bash
# List available templates
microgen templates list

# Search templates
microgen templates search "react"

# Use template
microgen create my-project --template template-name
```

### From Git Repository

```bash
microgen create my-project --template git+https://github.com/user/template.git
```

### From Local Directory

```bash
microgen create my-project --template ./my-local-template
```

## Coming Soon

This documentation section is under development. The template registry will include:

- Searchable template catalog
- Template ratings and reviews
- Usage statistics
- Community contributions

## Next Steps

- [**Custom Templates**](./custom-templates) - Create your own templates
- [**Contributing**](./contributing-templates) - Share your templates
- [**Template Examples**](./template-examples) - Study existing templates
