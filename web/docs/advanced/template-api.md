---
sidebar_position: 3
---

# Template API Reference

Complete API reference for creating and customizing Microgen templates.

## Template Configuration

### template.json <PERSON>a

```typescript
interface TemplateConfig {
  name: string;
  version: string;
  description: string;
  author?: string;
  type: 'full' | 'api-only' | 'frontend-only' | 'custom';
  tags?: string[];
  prompts: Prompt[];
  conditions?: Condition[];
  dependencies?: Dependencies;
  scripts?: Scripts;
  hooks?: Hooks;
  helpers?: string[];
}
```

### Prompt Types

#### Input Prompt
```typescript
interface InputPrompt {
  name: string;
  type: 'input';
  message: string;
  default?: string;
  validate?: string | ValidateFunction;
  transform?: TransformFunction;
}
```

#### Select Prompt
```typescript
interface SelectPrompt {
  name: string;
  type: 'select';
  message: string;
  choices: Choice[];
  default?: string;
}

interface Choice {
  name: string;
  value: string;
  description?: string;
}
```

#### Multiselect Prompt
```typescript
interface MultiselectPrompt {
  name: string;
  type: 'multiselect';
  message: string;
  choices: Choice[];
  default?: string[];
  min?: number;
  max?: number;
}
```

#### Confirm Prompt
```typescript
interface ConfirmPrompt {
  name: string;
  type: 'confirm';
  message: string;
  default?: boolean;
}
```

## Template Context

### Available Variables

```typescript
interface TemplateContext {
  // User answers
  answers: Record<string, any>;
  
  // Template metadata
  template: {
    name: string;
    version: string;
    path: string;
  };
  
  // Project information
  project: {
    name: string;
    path: string;
    outputDir: string;
  };
  
  // Utilities
  utils: TemplateUtils;
  
  // Environment
  env: {
    nodeVersion: string;
    npmVersion: string;
    platform: string;
  };
}
```

### Template Utils

```typescript
interface TemplateUtils {
  // String utilities
  camelCase(str: string): string;
  kebabCase(str: string): string;
  pascalCase(str: string): string;
  snakeCase(str: string): string;
  upperCase(str: string): string;
  lowerCase(str: string): string;
  
  // File utilities
  fileExists(path: string): boolean;
  directoryExists(path: string): boolean;
  readFile(path: string): string;
  writeFile(path: string, content: string): void;
  copyFile(src: string, dest: string): void;
  deleteFile(path: string): void;
  
  // Template utilities
  renderTemplate(template: string, context: any): string;
  copyTemplate(src: string, dest: string, context: any): void;
  generateFile(path: string, content: string): void;
  
  // Validation utilities
  isValidPackageName(name: string): boolean;
  isValidEmail(email: string): boolean;
  isValidUrl(url: string): boolean;
  
  // Interactive utilities
  confirm(message: string): Promise<boolean>;
  input(message: string, defaultValue?: string): Promise<string>;
  select(message: string, choices: Choice[]): Promise<string>;
  
  // Command utilities
  runCommand(command: string, options?: ExecOptions): Promise<string>;
  installDependencies(packageManager?: 'npm' | 'yarn' | 'pnpm'): Promise<void>;
  
  // Git utilities
  initGit(): Promise<void>;
  addGitRemote(url: string): Promise<void>;
  commitChanges(message: string): Promise<void>;
}
```

## Handlebars Helpers

### Built-in Helpers

#### String Helpers
```handlebars
{{camelCase "hello-world"}}        <!-- helloWorld -->
{{kebabCase "HelloWorld"}}         <!-- hello-world -->
{{pascalCase "hello-world"}}       <!-- HelloWorld -->
{{snakeCase "HelloWorld"}}         <!-- hello_world -->
{{upperCase "hello"}}              <!-- HELLO -->
{{lowerCase "HELLO"}}              <!-- hello -->
```

#### Conditional Helpers
```handlebars
{{#if (eq framework "nestjs")}}
  NestJS specific code
{{/if}}

{{#if (ne database "sqlite")}}
  Non-SQLite configuration
{{/if}}

{{#if (gt port 3000)}}
  Port is greater than 3000
{{/if}}

{{#if (includes features "authentication")}}
  Authentication code
{{/if}}
```

#### Array Helpers
```handlebars
{{#each dependencies}}
  "{{@key}}": "{{this}}"{{#unless @last}},{{/unless}}
{{/each}}

{{join features ", "}}             <!-- feature1, feature2, feature3 -->
{{first features}}                 <!-- First feature -->
{{last features}}                  <!-- Last feature -->
```

#### Object Helpers
```handlebars
{{#with database}}
  Host: {{host}}
  Port: {{port}}
  Name: {{name}}
{{/with}}

{{lookup config "database.host"}}  <!-- Access nested properties -->
```

### Custom Helpers

```javascript
// helpers/custom-helpers.js
module.exports = {
  // Generate service port
  servicePort: (basePort, index) => basePort + index,
  
  // Generate environment variable name
  envVar: (name) => name.toUpperCase().replace(/-/g, '_'),
  
  // Generate Docker service name
  dockerService: (name) => name.toLowerCase().replace(/[^a-z0-9]/g, '-'),
  
  // Format date
  formatDate: (date, format = 'YYYY-MM-DD') => {
    // Date formatting logic
    return new Date(date).toISOString().split('T')[0];
  },
  
  // Generate random string
  randomString: (length = 32) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
};
```

## Template Hooks

### Hook Types

```typescript
interface Hooks {
  preGenerate?: string;    // Path to pre-generation hook
  postGenerate?: string;   // Path to post-generation hook
  preInstall?: string;     // Path to pre-install hook
  postInstall?: string;    // Path to post-install hook
}
```

### Hook Context

```typescript
interface HookContext extends TemplateContext {
  // Hook-specific data
  hook: {
    name: string;
    phase: 'pre-generate' | 'post-generate' | 'pre-install' | 'post-install';
  };
  
  // Progress tracking
  progress: {
    current: number;
    total: number;
    message: string;
  };
}
```

### Hook Examples

#### Pre-generation Hook
```javascript
// hooks/pre-generate.js
module.exports = async (context) => {
  const { answers, utils } = context;
  
  // Validate project name
  if (!utils.isValidPackageName(answers.projectName)) {
    throw new Error('Invalid project name');
  }
  
  // Set derived values
  answers.projectSlug = utils.kebabCase(answers.projectName);
  answers.projectClass = utils.pascalCase(answers.projectName);
  
  // Check prerequisites
  if (answers.database === 'postgresql') {
    const hasDocker = await utils.runCommand('docker --version').catch(() => false);
    if (!hasDocker) {
      const installDocker = await utils.confirm(
        'PostgreSQL requires Docker. Install Docker first?'
      );
      if (!installDocker) {
        process.exit(1);
      }
    }
  }
  
  console.log(`Generating ${answers.projectName}...`);
};
```

#### Post-generation Hook
```javascript
// hooks/post-generate.js
module.exports = async (context) => {
  const { answers, outputDir, utils } = context;
  
  // Install dependencies
  console.log('Installing dependencies...');
  await utils.installDependencies(answers.packageManager || 'npm');
  
  // Initialize git repository
  if (answers.git !== false) {
    await utils.initGit();
    await utils.commitChanges('Initial commit');
  }
  
  // Generate additional files
  if (answers.generateDocs) {
    await utils.generateFile(
      'API.md',
      generateApiDocs(answers)
    );
  }
  
  // Run initial build
  if (answers.runBuild) {
    await utils.runCommand('npm run build');
  }
  
  // Display next steps
  console.log('\n✅ Project generated successfully!');
  console.log('\nNext steps:');
  console.log(`  cd ${answers.projectName}`);
  console.log('  npm run dev');
};
```

## Conditional Logic

### Simple Conditions
```json
{
  "conditions": [
    {
      "when": "framework === 'nestjs'",
      "include": ["nestjs/**/*"]
    },
    {
      "when": "database === 'postgresql'",
      "include": ["database/postgresql/**/*"]
    }
  ]
}
```

### Complex Conditions
```json
{
  "conditions": [
    {
      "when": "framework === 'nestjs' && database === 'postgresql'",
      "include": ["nestjs/postgresql/**/*"]
    },
    {
      "when": "features.includes('authentication') && authMethod === 'jwt'",
      "include": ["auth/jwt/**/*"]
    },
    {
      "when": "deployment === 'kubernetes' && monitoring === true",
      "include": ["k8s/monitoring/**/*"]
    }
  ]
}
```

### Dynamic Conditions
```javascript
// conditions/dynamic.js
module.exports = (answers) => {
  const conditions = [];
  
  // Add framework-specific conditions
  if (answers.framework === 'nestjs') {
    conditions.push({
      include: ['nestjs/**/*']
    });
    
    if (answers.features.includes('graphql')) {
      conditions.push({
        include: ['nestjs/graphql/**/*']
      });
    }
  }
  
  // Add database-specific conditions
  if (answers.database !== 'none') {
    conditions.push({
      include: [`database/${answers.database}/**/*`]
    });
  }
  
  return conditions;
};
```

## File Processing

### File Patterns

```typescript
interface FilePattern {
  pattern: string;
  processor?: 'handlebars' | 'ejs' | 'mustache' | 'custom';
  rename?: string | RenameFunction;
  condition?: string | ConditionFunction;
}
```

### Custom Processors

```javascript
// processors/custom-processor.js
module.exports = {
  name: 'custom',
  extensions: ['.custom'],
  
  process: (content, context) => {
    // Custom processing logic
    return content
      .replace(/\{\{PROJECT_NAME\}\}/g, context.answers.projectName)
      .replace(/\{\{TIMESTAMP\}\}/g, new Date().toISOString());
  }
};
```

### File Renaming

```javascript
// File renaming function
const renameFunction = (filename, context) => {
  return filename
    .replace('__PROJECT_NAME__', context.answers.projectName)
    .replace('__FRAMEWORK__', context.answers.framework);
};
```

## Validation

### Built-in Validators

```typescript
interface Validators {
  required: (value: string) => boolean | string;
  email: (value: string) => boolean | string;
  url: (value: string) => boolean | string;
  packageName: (value: string) => boolean | string;
  port: (value: number) => boolean | string;
  semver: (value: string) => boolean | string;
}
```

### Custom Validators

```javascript
// validators/custom.js
module.exports = {
  serviceName: (value) => {
    if (!value) return 'Service name is required';
    if (!/^[a-z][a-z0-9-]*[a-z0-9]$/.test(value)) {
      return 'Service name must be lowercase with hyphens';
    }
    return true;
  },
  
  databaseName: (value) => {
    if (!value) return 'Database name is required';
    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(value)) {
      return 'Database name must start with letter and contain only letters, numbers, and underscores';
    }
    return true;
  }
};
```

## Error Handling

### Template Errors

```typescript
class TemplateError extends Error {
  constructor(
    message: string,
    public code: string,
    public context?: any
  ) {
    super(message);
    this.name = 'TemplateError';
  }
}

// Error codes
enum TemplateErrorCode {
  INVALID_CONFIG = 'INVALID_CONFIG',
  MISSING_FILE = 'MISSING_FILE',
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  HOOK_FAILED = 'HOOK_FAILED',
  GENERATION_FAILED = 'GENERATION_FAILED'
}
```

### Error Recovery

```javascript
// Error handling in hooks
module.exports = async (context) => {
  try {
    await riskyOperation();
  } catch (error) {
    // Log error
    console.error('Operation failed:', error.message);
    
    // Attempt recovery
    const retry = await context.utils.confirm('Retry operation?');
    if (retry) {
      await riskyOperation();
    } else {
      // Graceful degradation
      console.warn('Skipping optional operation');
    }
  }
};
```

## Performance Optimization

### Lazy Loading

```javascript
// Lazy load heavy dependencies
const loadHeavyDependency = () => {
  return require('heavy-dependency');
};

module.exports = async (context) => {
  if (context.answers.needsHeavyFeature) {
    const heavy = loadHeavyDependency();
    await heavy.process();
  }
};
```

### Caching

```javascript
// Cache expensive operations
const cache = new Map();

const expensiveOperation = (input) => {
  if (cache.has(input)) {
    return cache.get(input);
  }
  
  const result = performExpensiveCalculation(input);
  cache.set(input, result);
  return result;
};
```

## Testing Templates

### Unit Tests

```javascript
// test/template.test.js
const { renderTemplate, validateConfig } = require('../lib/template');

describe('Template Rendering', () => {
  test('should render basic template', () => {
    const template = 'Hello {{name}}!';
    const context = { name: 'World' };
    const result = renderTemplate(template, context);
    
    expect(result).toBe('Hello World!');
  });
  
  test('should validate template config', () => {
    const config = {
      name: 'test-template',
      version: '1.0.0',
      prompts: []
    };
    
    const validation = validateConfig(config);
    expect(validation.valid).toBe(true);
  });
});
```

### Integration Tests

```javascript
// test/integration.test.js
const { generateProject } = require('../lib/generator');
const fs = require('fs-extra');

describe('Project Generation', () => {
  afterEach(async () => {
    await fs.remove('./test-output');
  });
  
  test('should generate complete project', async () => {
    const result = await generateProject('test-template', {
      projectName: 'test-project',
      framework: 'nestjs'
    }, './test-output');
    
    expect(fs.existsSync('./test-output/package.json')).toBe(true);
    expect(fs.existsSync('./test-output/src/main.ts')).toBe(true);
  });
});
```

## Next Steps

- [**Template Examples**](./template-examples) - Ready-to-use examples
- [**Contributing Templates**](./contributing-templates) - Share your templates
- [**Custom Templates Guide**](./custom-templates) - Create your own templates
- [**Best Practices**](./best-practices) - Template development guidelines
