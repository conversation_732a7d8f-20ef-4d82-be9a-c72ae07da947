---
sidebar_position: 4
---

# Contributing Templates

<PERSON><PERSON> how to contribute templates to the Microgen community and help other developers get started faster.

## Getting Started

### Template Repository Structure

```
microgen-templates/
├── templates/
│   ├── basic-api/
│   ├── react-spa/
│   ├── nextjs-app/
│   └── enterprise-fullstack/
├── docs/
│   ├── template-guidelines.md
│   └── testing.md
├── scripts/
│   ├── validate-templates.js
│   └── publish-templates.js
├── .github/
│   └── workflows/
│       ├── validate.yml
│       └── publish.yml
└── README.md
```

### Contributing Process

1. **Fork the Repository**
   ```bash
   git clone https://github.com/microgen-cli/templates.git
   cd templates
   ```

2. **Create a New Template**
   ```bash
   mkdir templates/my-awesome-template
   cd templates/my-awesome-template
   ```

3. **Develop Your Template**
   - Create `template.json`
   - Add template files
   - Write hooks (if needed)
   - Add documentation

4. **Test Your Template**
   ```bash
   npm run test:template my-awesome-template
   ```

5. **Submit Pull Request**
   - Create feature branch
   - Commit changes
   - Open pull request

## Template Guidelines

### Naming Conventions

**Template Names:**
- Use kebab-case: `react-typescript-app`
- Be descriptive: `nestjs-microservice-kafka`
- Include key technologies: `nextjs-tailwind-auth`

**File Names:**
- Use consistent naming patterns
- Include framework/technology indicators
- Use `.hbs` extension for Handlebars templates

### Template Structure

```
my-template/
├── template.json           # Template configuration
├── README.md              # Template documentation
├── files/                 # Template files
│   ├── package.json.hbs
│   ├── src/
│   │   └── main.ts.hbs
│   └── docker-compose.yml.hbs
├── hooks/                 # Optional hooks
│   ├── pre-generate.js
│   └── post-generate.js
└── test/                  # Template tests
    ├── answers.json
    └── expectations.js
```

### Configuration Standards

```json
{
  "name": "template-name",
  "version": "1.0.0",
  "description": "Clear, concise description",
  "author": "Your Name <<EMAIL>>",
  "type": "full|api-only|frontend-only",
  "tags": ["react", "typescript", "tailwind"],
  "license": "MIT",
  "repository": "https://github.com/user/template",
  "prompts": [
    {
      "name": "projectName",
      "type": "input",
      "message": "Project name:",
      "validate": "required"
    }
  ]
}
```

### Documentation Requirements

#### README.md Template

```markdown
# Template Name

Brief description of what this template creates.

## Features

- ✅ Feature 1
- ✅ Feature 2
- ✅ Feature 3

## Technologies

- Framework/Library
- Database
- Styling
- Authentication

## Usage

\`\`\`bash
microgen create my-project --template template-name
\`\`\`

## Generated Structure

\`\`\`
project/
├── src/
├── package.json
└── README.md
\`\`\`

## Configuration

Describe any configuration options or environment variables.

## Development

Instructions for local development.

## License

MIT
```

## Quality Standards

### Code Quality

**TypeScript Templates:**
- Use strict TypeScript configuration
- Include proper type definitions
- Follow consistent naming conventions
- Add JSDoc comments for public APIs

**JavaScript Templates:**
- Use modern ES6+ syntax
- Include ESLint configuration
- Follow consistent code style
- Add proper error handling

### Security Standards

**Dependencies:**
- Use latest stable versions
- Avoid packages with known vulnerabilities
- Include security-focused packages (helmet, cors)
- Regular dependency updates

**Configuration:**
- No hardcoded secrets
- Use environment variables
- Include security headers
- Implement proper authentication

### Performance Standards

**Build Optimization:**
- Minimize bundle sizes
- Use code splitting
- Optimize images and assets
- Include performance monitoring

**Runtime Performance:**
- Efficient database queries
- Proper caching strategies
- Resource optimization
- Memory leak prevention

## Testing Requirements

### Template Tests

```javascript
// test/template.test.js
const { generateProject, validateProject } = require('@microgen/testing');

describe('My Awesome Template', () => {
  const templatePath = __dirname + '/..';
  
  test('should generate basic project', async () => {
    const answers = {
      projectName: 'test-project',
      framework: 'react',
      styling: 'tailwind'
    };
    
    const result = await generateProject(templatePath, answers);
    
    expect(result.success).toBe(true);
    expect(result.files).toContain('package.json');
    expect(result.files).toContain('src/App.tsx');
  });
  
  test('should validate generated project', async () => {
    const validation = await validateProject('./test-output');
    
    expect(validation.valid).toBe(true);
    expect(validation.errors).toHaveLength(0);
  });
  
  test('should build successfully', async () => {
    const buildResult = await runCommand('npm run build', './test-output');
    
    expect(buildResult.exitCode).toBe(0);
  });
});
```

### Answer Variations

```json
// test/answers/basic.json
{
  "projectName": "basic-test",
  "framework": "react",
  "styling": "css-modules",
  "testing": false
}

// test/answers/full-features.json
{
  "projectName": "full-test",
  "framework": "react",
  "styling": "tailwind",
  "testing": true,
  "authentication": true,
  "database": "postgresql"
}
```

### Automated Testing

```yaml
# .github/workflows/test-templates.yml
name: Test Templates

on:
  pull_request:
    paths:
      - 'templates/**'

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        template: [basic-api, react-spa, nextjs-app]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 18
    
    - name: Install dependencies
      run: npm ci
    
    - name: Test template
      run: npm run test:template ${{ matrix.template }}
    
    - name: Validate generated project
      run: npm run validate:project ./test-output/${{ matrix.template }}
```

## Review Process

### Submission Checklist

- [ ] Template follows naming conventions
- [ ] Includes complete documentation
- [ ] All tests pass
- [ ] No security vulnerabilities
- [ ] Follows code quality standards
- [ ] Includes proper error handling
- [ ] Works with latest Microgen version

### Review Criteria

**Functionality:**
- Template generates working project
- All features work as expected
- Proper error handling
- Good user experience

**Code Quality:**
- Clean, readable code
- Consistent formatting
- Proper documentation
- Following best practices

**Maintainability:**
- Clear structure
- Modular design
- Easy to update
- Good test coverage

### Feedback Process

1. **Initial Review**
   - Automated checks
   - Basic functionality test
   - Documentation review

2. **Detailed Review**
   - Code quality assessment
   - Security review
   - Performance evaluation

3. **Community Feedback**
   - Public review period
   - Community testing
   - Feedback incorporation

4. **Final Approval**
   - Maintainer approval
   - Merge to main branch
   - Publication to registry

## Template Categories

### Official Templates

**Maintained by Microgen team:**
- Basic templates for common use cases
- Reference implementations
- Best practice examples

**Requirements:**
- Highest quality standards
- Comprehensive documentation
- Regular maintenance
- Community support

### Community Templates

**Contributed by community:**
- Specialized use cases
- Framework-specific templates
- Industry-specific solutions

**Requirements:**
- Good quality standards
- Basic documentation
- Working functionality
- Community maintenance

### Experimental Templates

**Cutting-edge features:**
- New technologies
- Experimental patterns
- Beta features

**Requirements:**
- Clearly marked as experimental
- May have breaking changes
- Limited support

## Maintenance

### Template Updates

**Regular Updates:**
- Dependency updates
- Security patches
- Bug fixes
- Documentation updates

**Version Management:**
- Semantic versioning
- Changelog maintenance
- Migration guides
- Deprecation notices

### Community Support

**Issue Management:**
- Bug reports
- Feature requests
- Usage questions
- Documentation improvements

**Community Engagement:**
- Discord discussions
- GitHub discussions
- Template showcases
- User feedback

## Recognition

### Contributor Recognition

**Hall of Fame:**
- Top contributors
- Template authors
- Community helpers

**Badges:**
- Template author badges
- Contribution levels
- Special recognitions

### Template Promotion

**Featured Templates:**
- Homepage showcase
- Blog posts
- Social media
- Conference talks

**Template Statistics:**
- Usage metrics
- Download counts
- Community ratings
- Success stories

## Getting Help

### Resources

- [Template Development Guide](./template-api)
- [Testing Framework](./testing)
- [Best Practices](./best-practices)
- [Community Discord](https://discord.gg/microgen)

### Support Channels

- **GitHub Issues**: Bug reports and feature requests
- **Discord**: Real-time help and discussions
- **Discussions**: Long-form discussions and Q&A
- **Email**: Direct contact with maintainers

## Next Steps

- [**Template Examples**](./template-examples) - Study existing templates
- [**Template API**](./template-api) - Learn the API
- [**Custom Templates**](./custom-templates) - Create your own
- [**Best Practices**](./best-practices) - Follow guidelines
