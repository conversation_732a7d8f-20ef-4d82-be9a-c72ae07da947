---
sidebar_position: 1
---

# Welcome to Microgen

**Microgen** is a modern command-line tool designed to accelerate the development of microservice architectures. Generate complete projects with both frontend and backend components in seconds, not hours.

## 🚀 What is Microgen?

Microgen is a comprehensive project generator that creates production-ready microservice applications with:

- **Modern Backend Frameworks**: NestJS, Express.js
- **Popular Frontend Technologies**: Next.js, React, Vue.js, Angular, Svelte
- **Microservice Communication**: Kafka, gRPC, Redis, TCP, NATS, RabbitMQ
- **Database Support**: PostgreSQL, MySQL, MongoDB, SQLite
- **Styling Options**: Tailwind CSS, Styled Components, CSS Modules, Sass/SCSS
- **DevOps Ready**: Docker, Docker Compose, health checks, environment configuration

## ✨ Key Features

### 🎯 **Rapid Development**
Generate complete microservice projects in seconds with interactive prompts or command-line options.

### 🏗️ **Production Ready**
All generated code follows best practices with TypeScript support, proper error handling, and comprehensive configuration.

### 🔧 **Highly Configurable**
Choose from multiple frameworks, databases, styling options, and microservice communication patterns.

### 📦 **Template Variety**
- **Full Stack**: Complete application with frontend and backend
- **API Only**: Backend microservices with database integration
- **Frontend Only**: Modern frontend applications with API integration

### 🐳 **DevOps Integration**
- Multi-stage Docker builds
- Docker Compose for local development
- Health check endpoints
- Environment-based configuration

## 🎯 Perfect For

- **Microservice Architects** building distributed systems
- **Full-Stack Developers** creating modern web applications
- **DevOps Engineers** setting up containerized applications
- **Teams** wanting consistent project structure and best practices
- **Startups** needing rapid prototyping and MVP development

## 🚀 Quick Start

Get started with Microgen in just a few commands:

```bash
# Install globally
npm install -g microgen

# Create a new project
microgen create my-awesome-project

# Or use npx (no installation required)
npx microgen create my-awesome-project
```

The CLI will guide you through an interactive setup process to configure your project exactly how you want it.

## 📚 What's Next?

- [**Installation Guide**](./installation) - Set up Microgen on your system
- [**Quick Start Tutorial**](./quick-start) - Create your first project in 5 minutes
- [**CLI Commands Reference**](./commands/create) - Explore all available commands and options
- [**Project Templates**](./templates/overview) - Learn about different project types
- [**Examples**](./examples/basic-api) - See real-world usage examples
