---
sidebar_position: 2
---

# Command Options

This page provides a comprehensive reference for all command-line options available in Microservice CLI.

## Global Options

These options are available for all commands:

### `--version`
Display the current version of Microservice CLI.

```bash
microservice-cli --version
# Output: 1.0.0
```

### `--help`
Display help information for the CLI or specific commands.

```bash
# Global help
microservice-cli --help

# Command-specific help
microservice-cli create --help
```

### `-v, --verbose`
Enable verbose output for debugging and detailed information.

```bash
microservice-cli create my-project --verbose
```

**Output includes:**
- Detailed file generation logs
- Template processing information
- Dependency installation progress
- Error stack traces

## Create Command Options

### Project Configuration

#### `-o, --output-dir <directory>`
Specify the output directory for the generated project.

```bash
# Relative path
microservice-cli create my-project --output-dir ./projects

# Absolute path
microservice-cli create my-project --output-dir /home/<USER>/development

# Home directory shorthand
microservice-cli create my-project --output-dir ~/projects
```

**Default**: Current working directory (`./`)

#### `-f, --force`
Overwrite existing files and directories without confirmation.

```bash
microservice-cli create my-project --force
```

**Use cases:**
- Regenerating a project with updated templates
- Overwriting incomplete project generation
- Automation scripts where confirmation isn't possible

⚠️ **Warning**: This will permanently delete existing files.

### Template Options

#### `-t, --template <template>`
Choose a specific project template.

```bash
# Available templates
microservice-cli create my-project --template full
microservice-cli create my-project --template api-only
microservice-cli create my-project --template frontend-only
```

**Template Details:**

| Template | Description | Includes |
|----------|-------------|----------|
| `full` | Complete full-stack application | Backend + Frontend + Database + Docker |
| `api-only` | Backend microservice only | API + Database + Microservices + Docker |
| `frontend-only` | Frontend application only | Frontend + Styling + State Management |

### Interaction Options

#### `-i, --interactive` / `--no-interactive`
Control interactive mode behavior.

```bash
# Enable interactive mode (default)
microservice-cli create my-project --interactive

# Disable interactive mode
microservice-cli create my-project --no-interactive
```

**Interactive Mode Features:**
- Step-by-step configuration prompts
- Real-time validation
- Help text for each option
- Ability to go back and change answers

**Non-Interactive Mode:**
- Uses default values or provided options
- Perfect for automation and CI/CD
- Faster execution
- Requires all necessary options to be provided

### Preview Options

#### `--dry-run`
Preview what would be generated without creating any files.

```bash
microservice-cli create my-project --dry-run
```

**Output includes:**
- Project structure tree
- List of files that would be created
- Configuration summary
- Template variables and their values

**Perfect for:**
- Testing configurations
- Previewing project structure
- Validating templates
- CI/CD pipeline testing

## Option Combinations

### Common Combinations

#### Quick API Generation
```bash
microservice-cli create user-api \
  --template api-only \
  --no-interactive \
  --output-dir ./services
```

#### Preview Full-Stack Project
```bash
microservice-cli create ecommerce-platform \
  --template full \
  --dry-run \
  --verbose
```

#### Force Regeneration
```bash
microservice-cli create my-project \
  --force \
  --verbose
```

#### Automated Project Creation
```bash
microservice-cli create payment-service \
  --template api-only \
  --no-interactive \
  --output-dir ./microservices \
  --force
```

## Environment Variables

You can set default values using environment variables:

### Available Environment Variables

```bash
# Default author name
export MICROSERVICE_CLI_AUTHOR="Your Name"

# Default license
export MICROSERVICE_CLI_LICENSE="MIT"

# Default output directory
export MICROSERVICE_CLI_OUTPUT_DIR="./projects"

# Default template
export MICROSERVICE_CLI_TEMPLATE="full"

# Enable verbose mode by default
export MICROSERVICE_CLI_VERBOSE="true"

# Disable interactive mode by default
export MICROSERVICE_CLI_INTERACTIVE="false"
```

### Usage Example
```bash
# Set environment variables
export MICROSERVICE_CLI_AUTHOR="John Doe"
export MICROSERVICE_CLI_OUTPUT_DIR="~/development"

# Create project (will use environment defaults)
microservice-cli create my-project
```

## Configuration File

Create a configuration file to set default options:

### Location
- **Global**: `~/.microservice-cli.json`
- **Project**: `./.microservice-cli.json`

### Format
```json
{
  "author": "Your Name",
  "license": "MIT",
  "outputDir": "./projects",
  "template": "full",
  "interactive": true,
  "verbose": false,
  "force": false
}
```

### Precedence Order
1. Command-line options (highest priority)
2. Environment variables
3. Project configuration file
4. Global configuration file
5. Built-in defaults (lowest priority)

## Validation Rules

### Project Name Validation
```bash
# Valid project names
microservice-cli create my-project        ✅
microservice-cli create user-api          ✅
microservice-cli create payment_service   ✅
microservice-cli create api123            ✅

# Invalid project names
microservice-cli create my project        ❌ (spaces)
microservice-cli create my@project        ❌ (special chars)
microservice-cli create 123project        ❌ (starts with number)
microservice-cli create -project          ❌ (starts with hyphen)
```

### Output Directory Validation
- Must be a valid path
- Parent directory must exist (unless using `--force`)
- Must have write permissions

### Template Validation
- Must be one of: `full`, `api-only`, `frontend-only`
- Case-sensitive

## Error Handling

### Common Option Errors

#### Invalid Template
```bash
microservice-cli create my-project --template invalid
# Error: Invalid template 'invalid'. Available templates: full, api-only, frontend-only
```

#### Invalid Output Directory
```bash
microservice-cli create my-project --output-dir /invalid/path
# Error: Output directory '/invalid/path' does not exist or is not writable
```

#### Missing Required Arguments
```bash
microservice-cli create
# Error: Missing required argument 'project-name'
```

## Tips and Best Practices

### 1. Use Dry Run First
Always preview your project structure before generation:
```bash
microservice-cli create my-project --dry-run
```

### 2. Combine with Verbose for Debugging
When troubleshooting, use both options:
```bash
microservice-cli create my-project --verbose --dry-run
```

### 3. Set Up Environment Variables
For consistent development experience:
```bash
# Add to your shell profile (.bashrc, .zshrc, etc.)
export MICROSERVICE_CLI_AUTHOR="Your Name"
export MICROSERVICE_CLI_OUTPUT_DIR="~/development"
```

### 4. Use Configuration Files for Teams
Create a team configuration file:
```json
{
  "author": "Team Name",
  "license": "MIT",
  "template": "full",
  "outputDir": "./projects"
}
```

### 5. Automation-Friendly Commands
For CI/CD and scripts:
```bash
microservice-cli create $PROJECT_NAME \
  --template $TEMPLATE \
  --no-interactive \
  --force \
  --output-dir $OUTPUT_DIR
```

## Next Steps

- **[Project Templates](../templates/overview)** - Learn about available templates
- **[Configuration Guide](../configuration/project-config)** - Detailed configuration options
- **[Examples](../examples/basic-api)** - Real-world usage examples
