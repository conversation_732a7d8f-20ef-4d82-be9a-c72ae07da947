---
sidebar_position: 1
---

# Create Command

The `create` command is the main command for generating new microservice projects. It supports both interactive and non-interactive modes with extensive customization options.

## Basic Usage

```bash
microgen create <project-name> [options]
```

### Examples

```bash
# Interactive mode (default)
microgen create my-project

# Non-interactive with template
microgen create my-api --template api-only --no-interactive

# With custom output directory
microgen create my-project --output-dir ./projects

# Dry run to preview what will be generated
microgen create my-project --dry-run
```

## Command Syntax

```bash
microgen create <project-name> [options]

# Alternative alias
microgen new <project-name> [options]
```

## Arguments

### `<project-name>`
- **Required**: Yes
- **Type**: String
- **Description**: Name of the project to create
- **Validation**: Must be a valid directory name (alphanumeric, hyphens, underscores)

**Examples:**
```bash
microgen create my-awesome-api
microgen create user-management-service
microgen create ecommerce_platform
```

## Options

### `-o, --output-dir <directory>`
- **Type**: String
- **Default**: Current working directory
- **Description**: Specify where to create the project

```bash
microgen create my-project --output-dir ./projects
microgen create my-project -o ~/development/microservices
```

### `-f, --force`
- **Type**: Boolean
- **Default**: false
- **Description**: Overwrite existing files and directories

```bash
microgen create my-project --force
```

⚠️ **Warning**: This will overwrite existing files without confirmation.

### `-i, --interactive`
- **Type**: Boolean
- **Default**: true
- **Description**: Enable or disable interactive mode

```bash
# Disable interactive mode
microgen create my-project --no-interactive

# Explicitly enable interactive mode
microgen create my-project --interactive
```

### `-t, --template <template>`
- **Type**: String
- **Options**: `full`, `api-only`, `frontend-only`
- **Default**: Determined by interactive prompts
- **Description**: Choose a project template

```bash
# API-only backend service
microgen create my-api --template api-only

# Frontend-only application
microgen create my-frontend --template frontend-only

# Full-stack application
microgen create my-app --template full
```

### `-v, --verbose`
- **Type**: Boolean
- **Default**: false
- **Description**: Enable verbose output for debugging

```bash
microgen create my-project --verbose
```

### `--dry-run`
- **Type**: Boolean
- **Default**: false
- **Description**: Show what would be generated without creating files

```bash
microgen create my-project --dry-run
```

This is perfect for:
- Previewing project structure
- Testing configurations
- CI/CD validation

## Interactive Mode

When running in interactive mode (default), the CLI will prompt you for configuration:

### Project Configuration
```
? Project description: (my-project microservice project)
? Author: Your Name
? Version: (1.0.0)
? License: (MIT)
```

### Backend Service Configuration
```
? Service name: (api)
? Service type: 
  ❯ API
    Web
    Worker
    Gateway
? Framework:
  ❯ NestJS
    Express.js
? Port: (3000)
? Database:
  ❯ PostgreSQL
    MySQL
    MongoDB
    SQLite
? Enable authentication? (Y/n)
? Enable CORS? (Y/n)
? Enable Swagger documentation? (Y/n)
? Microservice transport methods: (Use space to select)
  ❯◯ Kafka
   ◯ gRPC
   ◯ Redis
   ◯ TCP
   ◯ NATS
   ◯ RabbitMQ
? Enable message patterns? (Y/n)
? Add another service? (y/N)
```

### Frontend Configuration
```
? Include frontend? (Y/n)
? Frontend framework:
  ❯ Next.js
    React
    Vue.js
    Angular
    Svelte
? Styling framework:
  ❯ Tailwind CSS
    Styled Components
    CSS Modules
    Sass/SCSS
? State management:
  ❯ Context API
    Redux
    Zustand
? API base URL: (http://localhost:3000)
```

## Non-Interactive Mode

Use non-interactive mode for automation, CI/CD, or when you know exactly what you want:

```bash
microgen create my-api \
  --template api-only \
  --no-interactive \
  --output-dir ./services
```

In non-interactive mode, the CLI uses sensible defaults:
- **Backend**: NestJS with PostgreSQL
- **Frontend**: Next.js with Tailwind CSS
- **Authentication**: Enabled
- **Microservices**: Kafka + gRPC transport

## Templates

### Full Stack (`full`)
Generates a complete application with:
- Backend API service (NestJS or Express.js)
- Frontend application (Next.js, React, Vue.js, Angular, or Svelte)
- Database integration
- Docker configuration
- Development environment setup

### API Only (`api-only`)
Generates backend services only:
- API service with chosen framework
- Database integration
- Microservice communication setup
- Authentication and authorization
- Swagger documentation
- Docker configuration

### Frontend Only (`frontend-only`)
Generates frontend applications only:
- Modern frontend framework
- Styling framework integration
- State management setup
- API client configuration
- Build and deployment configuration

## Output Structure

### Full Stack Project
```
my-project/
├── services/
│   └── api/                 # Backend service
├── frontend/                # Frontend application
├── docker-compose.yml       # Development environment
├── package.json            # Root package.json with scripts
└── README.md               # Project documentation
```

### API Only Project
```
my-api/
├── src/
│   ├── main.ts
│   ├── app.module.ts
│   ├── microservice/
│   ├── auth/
│   └── database/
├── package.json
├── Dockerfile
├── docker-compose.yml
└── README.md
```

### Frontend Only Project
```
my-frontend/
├── app/                    # Next.js app directory
├── components/
├── lib/
├── package.json
├── Dockerfile
└── README.md
```

## Error Handling

### Common Errors

#### Directory Already Exists
```
Error: Directory already exists at "/path/to/my-project"!
```
**Solution**: Use `--force` flag or choose a different name/location.

#### Invalid Project Name
```
Error: Project name contains invalid characters
```
**Solution**: Use only alphanumeric characters, hyphens, and underscores.

#### Permission Denied
```
Error: Permission denied when creating directory
```
**Solution**: Check directory permissions or use a different output directory.

#### Network Issues
```
Error: Failed to install dependencies
```
**Solution**: Check internet connection and npm registry access.

## Advanced Usage

### Combining Options
```bash
# Create API-only project with verbose output in custom directory
microgen create payment-service \
  --template api-only \
  --output-dir ./microservices \
  --verbose \
  --force

# Preview full-stack project structure
microgen create ecommerce-platform \
  --template full \
  --dry-run
```

### Environment Variables
You can set default values using environment variables:

```bash
export MICROGEN_AUTHOR="Your Name"
export MICROGEN_LICENSE="MIT"
export MICROGEN_OUTPUT_DIR="./projects"

microgen create my-project
```

### Configuration File
Create a `.microgen.json` file in your home directory:

```json
{
  "author": "Your Name",
  "license": "MIT",
  "defaultTemplate": "full",
  "outputDir": "./projects"
}
```

## Next Steps

After creating your project:

1. **[Explore Templates](../templates/overview)** - Learn about different project types
2. **[Configuration Guide](../configuration/project-config)** - Customize your setup
3. **[Examples](../examples/basic-api)** - See real-world usage examples
