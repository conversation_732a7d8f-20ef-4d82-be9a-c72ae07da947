---
sidebar_position: 1
---

# Template Overview

Microservice CLI provides three main project templates, each designed for different use cases and development scenarios. This guide explains each template and helps you choose the right one for your project.

## Available Templates

### 🚀 Full Stack (`full`)
Complete microservice application with both frontend and backend components.

### 🔧 API Only (`api-only`)
Backend microservice focused on API development and microservice communication.

### 🎨 Frontend Only (`frontend-only`)
Modern frontend application with API integration capabilities.

## Template Comparison

| Feature | Full Stack | API Only | Frontend Only |
|---------|------------|----------|---------------|
| **Backend API** | ✅ NestJS/Express | ✅ NestJS/Express | ❌ |
| **Frontend App** | ✅ Next.js/React/Vue/Angular/Svelte | ❌ | ✅ Next.js/React/Vue/Angular/Svelte |
| **Database** | ✅ PostgreSQL/MySQL/MongoDB/SQLite | ✅ PostgreSQL/MySQL/MongoDB/SQLite | ❌ |
| **Authentication** | ✅ JWT/OAuth | ✅ JWT/OAuth | ✅ Client-side auth |
| **Microservices** | ✅ Kafka/gRPC/Redis | ✅ Kafka/gRPC/Redis | ❌ |
| **Docker** | ✅ Multi-service | ✅ Single service | ✅ Single service |
| **API Documentation** | ✅ Swagger/OpenAPI | ✅ Swagger/OpenAPI | ❌ |
| **State Management** | ✅ Frontend state | ❌ | ✅ Redux/Zustand/Context |
| **Styling** | ✅ Tailwind/Styled Components | ❌ | ✅ Tailwind/Styled Components |

## When to Use Each Template

### 🚀 Full Stack Template

**Perfect for:**
- Complete web applications
- MVP development
- Prototyping full-featured products
- Small to medium-sized projects
- Teams wanting everything in one repository

**Use cases:**
- E-commerce platforms
- Content management systems
- Dashboard applications
- Social media platforms
- SaaS applications

**Example projects:**
```bash
# E-commerce platform
microservice-cli create ecommerce-platform --template full

# Admin dashboard
microservice-cli create admin-dashboard --template full

# Social media app
microservice-cli create social-app --template full
```

### 🔧 API Only Template

**Perfect for:**
- Microservice architectures
- Backend-focused development
- API-first development
- Service-oriented architectures
- Teams with separate frontend teams

**Use cases:**
- User management services
- Payment processing APIs
- Notification services
- Data processing services
- Integration APIs

**Example projects:**
```bash
# User authentication service
microservice-cli create auth-service --template api-only

# Payment processing API
microservice-cli create payment-api --template api-only

# Notification service
microservice-cli create notification-service --template api-only
```

### 🎨 Frontend Only Template

**Perfect for:**
- Frontend-focused development
- Consuming existing APIs
- Static site generation
- Client-side applications
- Teams with separate backend teams

**Use cases:**
- Marketing websites
- Admin panels for existing APIs
- Mobile app companions
- Documentation sites
- Landing pages

**Example projects:**
```bash
# Admin panel for existing API
microservice-cli create admin-panel --template frontend-only

# Marketing website
microservice-cli create company-website --template frontend-only

# Documentation site
microservice-cli create docs-site --template frontend-only
```

## Template Architecture

### Full Stack Architecture

```mermaid
graph TB
    subgraph "Frontend (Port 3001)"
        FE[Next.js/React/Vue/Angular/Svelte]
        FE_STYLE[Tailwind CSS/Styled Components]
        FE_STATE[Redux/Zustand/Context API]
    end
    
    subgraph "Backend (Port 3000)"
        API[NestJS/Express API]
        AUTH[JWT Authentication]
        SWAGGER[Swagger Documentation]
    end
    
    subgraph "Database"
        DB[(PostgreSQL/MySQL/MongoDB)]
    end
    
    subgraph "Microservices"
        KAFKA[Kafka]
        GRPC[gRPC]
        REDIS[Redis]
    end
    
    FE --> API
    API --> DB
    API --> KAFKA
    API --> GRPC
    API --> REDIS
```

### API Only Architecture

```mermaid
graph TB
    subgraph "API Service (Port 3000)"
        API[NestJS/Express API]
        AUTH[JWT Authentication]
        SWAGGER[Swagger Documentation]
        HEALTH[Health Checks]
    end
    
    subgraph "Database"
        DB[(PostgreSQL/MySQL/MongoDB)]
    end
    
    subgraph "Microservices"
        KAFKA[Kafka]
        GRPC[gRPC]
        REDIS[Redis]
        NATS[NATS]
        RABBITMQ[RabbitMQ]
    end
    
    subgraph "External Clients"
        WEB[Web Apps]
        MOBILE[Mobile Apps]
        OTHER[Other Services]
    end
    
    API --> DB
    API --> KAFKA
    API --> GRPC
    API --> REDIS
    WEB --> API
    MOBILE --> API
    OTHER --> API
```

### Frontend Only Architecture

```mermaid
graph TB
    subgraph "Frontend Application"
        APP[Next.js/React/Vue/Angular/Svelte]
        STYLE[Tailwind CSS/Styled Components]
        STATE[Redux/Zustand/Context API]
        AUTH[Client-side Authentication]
    end
    
    subgraph "External APIs"
        API1[Backend API 1]
        API2[Backend API 2]
        API3[Third-party APIs]
    end
    
    subgraph "Deployment"
        CDN[CDN/Static Hosting]
        DOCKER[Docker Container]
    end
    
    APP --> API1
    APP --> API2
    APP --> API3
    APP --> CDN
    APP --> DOCKER
```

## Technology Stacks

### Backend Technologies

#### NestJS (Recommended)
- **Enterprise-grade** Node.js framework
- **Microservice support** built-in
- **Dependency injection** and modular architecture
- **GraphQL and REST** API support
- **Extensive ecosystem** and decorators

#### Express.js
- **Minimal and flexible** Node.js framework
- **Fast development** and lightweight
- **Large ecosystem** of middleware
- **Simple and straightforward** API

### Frontend Technologies

#### Next.js (Recommended)
- **React framework** with SSR/SSG
- **File-based routing** and API routes
- **Optimized performance** and SEO
- **Built-in TypeScript** support

#### React
- **Component-based** UI library
- **Large ecosystem** and community
- **Flexible and powerful** state management
- **Vite integration** for fast development

#### Vue.js
- **Progressive framework** for building UIs
- **Easy to learn** and integrate
- **Excellent documentation** and tooling
- **Composition API** for better code organization

#### Angular
- **Full-featured framework** for large applications
- **TypeScript-first** development
- **Powerful CLI** and tooling
- **Enterprise-ready** with comprehensive features

#### Svelte
- **Compile-time optimized** framework
- **No virtual DOM** for better performance
- **Simple and intuitive** syntax
- **Small bundle sizes**

### Database Options

#### PostgreSQL (Recommended)
- **Advanced relational database** with JSON support
- **ACID compliance** and strong consistency
- **Excellent performance** and scalability
- **Rich feature set** and extensions

#### MySQL
- **Popular relational database** with wide adoption
- **Good performance** and reliability
- **Strong community** and ecosystem
- **Easy to deploy** and manage

#### MongoDB
- **Document-oriented NoSQL** database
- **Flexible schema** and horizontal scaling
- **JSON-like documents** for easy development
- **Built-in replication** and sharding

#### SQLite
- **Lightweight file-based** database
- **Zero configuration** and easy deployment
- **Perfect for development** and small applications
- **ACID compliance** in a single file

## Customization Options

### Styling Frameworks

#### Tailwind CSS (Recommended)
- **Utility-first** CSS framework
- **Rapid development** with pre-built classes
- **Highly customizable** design system
- **Excellent performance** with purging

#### Styled Components
- **CSS-in-JS** library for React
- **Component-scoped styling** and theming
- **Dynamic styling** based on props
- **Server-side rendering** support

#### CSS Modules
- **Localized CSS** with automatic class names
- **No naming conflicts** between components
- **Standard CSS** with enhanced features
- **Build-time optimization**

#### Sass/SCSS
- **CSS extension language** with variables
- **Nested rules** and mixins
- **Modular architecture** with partials
- **Mature ecosystem** and tooling

### State Management

#### Context API (Recommended for small apps)
- **Built into React** with no additional dependencies
- **Simple state sharing** between components
- **Good for small to medium** applications
- **Easy to understand** and implement

#### Redux
- **Predictable state container** for JavaScript apps
- **Time-travel debugging** and dev tools
- **Large ecosystem** of middleware
- **Perfect for complex** state management

#### Zustand
- **Small and fast** state management
- **Simple API** with minimal boilerplate
- **TypeScript-first** development
- **No providers** or complex setup

## Next Steps

Choose your template and dive deeper:

- **[Backend Configuration](../configuration/backend-config)** - Configure your API services
- **[Frontend Configuration](../configuration/frontend-config)** - Set up your frontend application
- **[Microservice Configuration](../configuration/microservices)** - Configure service communication
- **[Examples](../examples/basic-api)** - See real-world implementations
