---
sidebar_position: 4
---

# Microservices Configuration

Configure inter-service communication, service discovery, and distributed system patterns for your microservice architecture.

## Communication Patterns

### Synchronous Communication

#### gRPC
High-performance RPC framework for service-to-service communication.

**Proto Definition:**
```protobuf
// user.proto
syntax = "proto3";

package user;

service UserService {
  rpc GetUser(GetUserRequest) returns (User);
  rpc CreateUser(CreateUserRequest) returns (User);
  rpc UpdateUser(UpdateUserRequest) returns (User);
  rpc DeleteUser(DeleteUserRequest) returns (Empty);
}

message User {
  string id = 1;
  string email = 2;
  string name = 3;
  bool active = 4;
  string created_at = 5;
}

message GetUserRequest {
  string id = 1;
}

message CreateUserRequest {
  string email = 1;
  string name = 2;
}

message UpdateUserRequest {
  string id = 1;
  string email = 2;
  string name = 3;
  bool active = 4;
}

message DeleteUserRequest {
  string id = 1;
}

message Empty {}
```

**NestJS gRPC Server:**
```typescript
// main.ts
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.createMicroservice<MicroserviceOptions>(AppModule, {
    transport: Transport.GRPC,
    options: {
      package: 'user',
      protoPath: join(__dirname, 'user.proto'),
      url: '0.0.0.0:50051',
    },
  });

  await app.listen();
}
bootstrap();
```

**gRPC Controller:**
```typescript
// user.controller.ts
import { Controller } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { UserService } from './user.service';

interface User {
  id: string;
  email: string;
  name: string;
  active: boolean;
  created_at: string;
}

interface GetUserRequest {
  id: string;
}

interface CreateUserRequest {
  email: string;
  name: string;
}

@Controller()
export class UserController {
  constructor(private readonly userService: UserService) {}

  @GrpcMethod('UserService', 'GetUser')
  async getUser(data: GetUserRequest): Promise<User> {
    return this.userService.findById(data.id);
  }

  @GrpcMethod('UserService', 'CreateUser')
  async createUser(data: CreateUserRequest): Promise<User> {
    return this.userService.create(data);
  }
}
```

**gRPC Client:**
```typescript
// grpc-client.service.ts
import { Injectable, OnModuleInit } from '@nestjs/common';
import { Client, ClientGrpc, Transport } from '@nestjs/microservices';
import { join } from 'path';

interface UserService {
  getUser(data: { id: string }): Observable<any>;
  createUser(data: { email: string; name: string }): Observable<any>;
}

@Injectable()
export class GrpcClientService implements OnModuleInit {
  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'user',
      protoPath: join(__dirname, 'user.proto'),
      url: 'localhost:50051',
    },
  })
  private client: ClientGrpc;

  private userService: UserService;

  onModuleInit() {
    this.userService = this.client.getService<UserService>('UserService');
  }

  async getUser(id: string) {
    return this.userService.getUser({ id }).toPromise();
  }

  async createUser(email: string, name: string) {
    return this.userService.createUser({ email, name }).toPromise();
  }
}
```

**Benefits:**
- Type-safe contracts with Protocol Buffers
- High performance binary serialization
- Bi-directional streaming support
- Language agnostic
- Built-in load balancing and service discovery

#### HTTP/REST
Traditional REST API communication between services.

**Features:**
- Simple implementation
- Wide tooling support
- Cacheable responses
- Stateless design

### Asynchronous Communication

#### Apache Kafka
Distributed event streaming platform for real-time data processing.

**NestJS Kafka Configuration:**
```typescript
// main.ts - Kafka microservice setup
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.createMicroservice<MicroserviceOptions>(AppModule, {
    transport: Transport.KAFKA,
    options: {
      client: {
        clientId: 'user-service',
        brokers: ['localhost:9092'],
      },
      consumer: {
        groupId: 'user-consumer',
      },
    },
  });

  await app.listen();
}
bootstrap();
```

**Kafka Producer Service:**
```typescript
// kafka-producer.service.ts
import { Injectable } from '@nestjs/common';
import { ClientKafka, Client, Transport } from '@nestjs/microservices';

@Injectable()
export class KafkaProducerService {
  @Client({
    transport: Transport.KAFKA,
    options: {
      client: {
        clientId: 'user-service',
        brokers: ['localhost:9092'],
      },
    },
  })
  client: ClientKafka;

  async onModuleInit() {
    await this.client.connect();
  }

  async publishUserCreated(userId: string, email: string) {
    return this.client.emit('user.created', {
      userId,
      email,
      timestamp: new Date().toISOString(),
    });
  }
}
```

**Kafka Consumer:**
```typescript
// user.controller.ts
import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';

@Controller()
export class UserController {
  @MessagePattern('user.created')
  async handleUserCreated(@Payload() data: any) {
    console.log('User created:', data);
    // Process the user creation event
    return { status: 'processed' };
  }

  @MessagePattern('user.updated')
  async handleUserUpdated(@Payload() data: any) {
    console.log('User updated:', data);
    // Process the user update event
    return { status: 'processed' };
  }
}
```

**Use Cases:**
- Event sourcing
- CQRS patterns
- Real-time analytics
- Log aggregation

#### Redis Pub/Sub
Lightweight publish-subscribe messaging.

**NestJS Redis Configuration:**
```typescript
// main.ts
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.createMicroservice<MicroserviceOptions>(AppModule, {
    transport: Transport.REDIS,
    options: {
      host: 'localhost',
      port: 6379,
    },
  });

  await app.listen();
}
bootstrap();
```

**Redis Publisher Service:**
```typescript
// redis-publisher.service.ts
import { Injectable } from '@nestjs/common';
import { ClientProxy, ClientProxyFactory, Transport } from '@nestjs/microservices';

@Injectable()
export class RedisPublisherService {
  private client: ClientProxy;

  constructor() {
    this.client = ClientProxyFactory.create({
      transport: Transport.REDIS,
      options: {
        host: 'localhost',
        port: 6379,
      },
    });
  }

  async publishUserCreated(userId: string, email: string) {
    return this.client.emit('user.created', {
      userId,
      email,
      timestamp: new Date().toISOString(),
    });
  }

  async publishUserUpdated(userId: string, changes: any) {
    return this.client.emit('user.updated', {
      userId,
      changes,
      timestamp: new Date().toISOString(),
    });
  }
}
```

**Redis Subscriber Controller:**
```typescript
// user.controller.ts
import { Controller } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';

@Controller()
export class UserController {
  @EventPattern('user.created')
  async handleUserCreated(@Payload() data: any) {
    console.log('User created event received:', data);
    // Process the user creation event
    // e.g., send welcome email, update analytics, etc.
  }

  @EventPattern('user.updated')
  async handleUserUpdated(@Payload() data: any) {
    console.log('User updated event received:', data);
    // Process the user update event
    // e.g., invalidate cache, update search index, etc.
  }
}
```

#### NATS
Cloud-native messaging system for microservices.

**Features:**
- Subject-based messaging
- Request-reply patterns
- Clustering support
- Lightweight protocol

#### RabbitMQ
Robust message broker with advanced routing.

**Patterns:**
- Work queues
- Publish/Subscribe
- Routing patterns
- RPC over messaging

## Service Discovery

### Static Configuration
Simple service registry for development and small deployments.

```yaml
# docker-compose.yml
services:
  user-service:
    image: user-service:latest
    ports:
      - "3001:3000"
    environment:
      - ORDER_SERVICE_URL=http://order-service:3000
      
  order-service:
    image: order-service:latest
    ports:
      - "3002:3000"
    environment:
      - USER_SERVICE_URL=http://user-service:3000
```

### Dynamic Discovery
Service registry with health checks and load balancing.

**Options:**
- Consul
- Eureka
- etcd
- Kubernetes DNS

## Load Balancing

### Client-side Load Balancing
Services handle load balancing logic.

```typescript
class ServiceClient {
  private endpoints = [
    'http://service-1:3000',
    'http://service-2:3000',
    'http://service-3:3000'
  ];
  
  private currentIndex = 0;
  
  getEndpoint() {
    const endpoint = this.endpoints[this.currentIndex];
    this.currentIndex = (this.currentIndex + 1) % this.endpoints.length;
    return endpoint;
  }
}
```

### Server-side Load Balancing
External load balancer distributes requests.

**Options:**
- NGINX
- HAProxy
- AWS ALB
- Kubernetes Ingress

## Circuit Breaker Pattern

Prevent cascading failures in distributed systems.

```typescript
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
  
  async call(fn: Function) {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }
    
    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
}
```

## API Gateway

Centralized entry point for microservice requests.

**Features:**
- Request routing
- Authentication
- Rate limiting
- Request/response transformation
- Monitoring and analytics

**Configuration Example:**
```yaml
# Kong Gateway
services:
  - name: user-service
    url: http://user-service:3000
    routes:
      - name: user-routes
        paths: ["/api/users"]
        
  - name: order-service
    url: http://order-service:3000
    routes:
      - name: order-routes
        paths: ["/api/orders"]
```

## Data Management

### Database per Service
Each service owns its data and database.

**Benefits:**
- Service independence
- Technology diversity
- Fault isolation
- Scalability

### Shared Database Anti-pattern
Avoid sharing databases between services.

**Problems:**
- Tight coupling
- Schema conflicts
- Deployment dependencies
- Performance bottlenecks

### Event Sourcing
Store events instead of current state.

```typescript
interface Event {
  id: string;
  aggregateId: string;
  type: string;
  data: any;
  timestamp: Date;
}

class EventStore {
  async append(streamId: string, events: Event[]) {
    // Store events in order
  }
  
  async getEvents(streamId: string): Promise<Event[]> {
    // Retrieve events for aggregate
  }
}
```

## Monitoring & Observability

### Distributed Tracing
Track requests across multiple services.

**Tools:**
- Jaeger
- Zipkin
- AWS X-Ray
- OpenTelemetry

### Centralized Logging
Aggregate logs from all services.

**Stack:**
- ELK (Elasticsearch, Logstash, Kibana)
- EFK (Elasticsearch, Fluentd, Kibana)
- Grafana Loki

### Metrics Collection
Monitor service health and performance.

**Metrics:**
- Request rate
- Error rate
- Response time
- Resource utilization

## Security

### Service-to-Service Authentication
Secure communication between services.

**Methods:**
- JWT tokens
- mTLS certificates
- API keys
- OAuth 2.0

### Network Security
Isolate services at the network level.

**Strategies:**
- Service mesh (Istio, Linkerd)
- Network policies
- VPC/subnet isolation
- Firewall rules

## Deployment Patterns

### Blue-Green Deployment
Zero-downtime deployments with environment switching.

### Canary Deployment
Gradual rollout to subset of users.

### Rolling Deployment
Sequential update of service instances.

## Docker Compose Example

Complete microservice setup with communication.

```yaml
version: '3.8'
services:
  # API Gateway
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - user-service
      - order-service
  
  # User Service
  user-service:
    build: ./user-service
    environment:
      - DATABASE_URL=***********************************/users
      - KAFKA_BROKERS=kafka:9092
    depends_on:
      - user-db
      - kafka
  
  user-db:
    image: postgres:13
    environment:
      - POSTGRES_DB=users
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
  
  # Order Service
  order-service:
    build: ./order-service
    environment:
      - DATABASE_URL=************************************/orders
      - USER_SERVICE_URL=http://user-service:3000
      - KAFKA_BROKERS=kafka:9092
    depends_on:
      - order-db
      - kafka
  
  order-db:
    image: postgres:13
    environment:
      - POSTGRES_DB=orders
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
  
  # Message Broker
  kafka:
    image: confluentinc/cp-kafka:latest
    environment:
      - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092
    depends_on:
      - zookeeper
  
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      - ZOOKEEPER_CLIENT_PORT=2181
  
  # Cache
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
```

## Best Practices

### Service Design
- Single responsibility principle
- Bounded contexts
- API versioning
- Backward compatibility

### Communication
- Prefer asynchronous communication
- Implement timeouts and retries
- Use circuit breakers
- Design for failure

### Data Management
- Eventual consistency
- Saga patterns for transactions
- Event-driven architecture
- CQRS when appropriate

## Next Steps

- [**Backend Configuration**](./backend-config) - Configure individual services
- [**Frontend Configuration**](./frontend-config) - Setup client applications
- [**Project Configuration**](./project-config) - Overall project setup
- [**Examples**](../examples/basic-api) - See microservices in action
