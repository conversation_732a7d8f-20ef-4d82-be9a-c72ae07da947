---
sidebar_position: 1
---

# Project Configuration

Learn how to configure your Microgen projects with various settings, templates, and customization options.

## Project Templates

Microgen offers three main project templates to suit different development needs:

### Full Stack Template (`full`)
Creates a complete microservice application with both frontend and backend components.

**Includes:**
- Backend API (NestJS or Express)
- Frontend application (Next.js, React, Vue, Angular, or Svelte)
- Database integration
- Docker containerization
- Inter-service communication setup

```bash
microgen create my-fullstack-app --template full
```

### API-Only Template (`api-only`)
Generates a backend-only microservice with API endpoints and database integration.

**Includes:**
- REST API with chosen framework
- Database models and migrations
- Authentication setup (optional)
- Swagger/OpenAPI documentation
- Docker configuration
- Health check endpoints

```bash
microgen create my-api --template api-only
```

### Frontend-Only Template (`frontend-only`)
Creates a frontend application optimized for microservice architecture.

**Includes:**
- Modern frontend framework setup
- API client configuration
- Styling framework integration
- Build optimization
- Docker configuration for deployment

```bash
microgen create my-frontend --template frontend-only
```

## Configuration Options

### Project Metadata

Configure basic project information:

```bash
# Project name (required)
microgen create my-project

# Custom output directory
microgen create my-project --output-dir ./projects

# Force overwrite existing files
microgen create my-project --force
```

### Interactive vs Non-Interactive Mode

**Interactive Mode (Default):**
- Guided setup with prompts
- Framework and feature selection
- Configuration validation
- Recommended settings

**Non-Interactive Mode:**
- Automated setup with defaults
- Faster for CI/CD pipelines
- Requires template specification

```bash
# Interactive mode (default)
microgen create my-project

# Non-interactive mode
microgen create my-project --no-interactive --template full
```

### Environment Configuration

Microgen generates environment-specific configuration files:

- `.env.development` - Development settings
- `.env.production` - Production settings
- `.env.test` - Testing configuration
- `.env.example` - Template for team sharing

### Docker Configuration

All templates include Docker support:

- `Dockerfile` - Container definition
- `docker-compose.yml` - Multi-service orchestration
- `.dockerignore` - Optimized build context

## Advanced Configuration

### Custom Templates

Create your own project templates for consistent team standards:

```bash
# Use custom template directory
microgen create my-project --template-dir ./custom-templates
```

### Configuration Files

Microgen respects configuration files in your home directory:

**`~/.microgen/config.json`:**
```json
{
  "defaultAuthor": "Your Name",
  "defaultEmail": "<EMAIL>",
  "defaultLicense": "MIT",
  "defaultTemplate": "full",
  "checkUpdates": true
}
```

### Environment Variables

Override default settings with environment variables:

```bash
export MICROGEN_AUTHOR="Your Name"
export MICROGEN_LICENSE="MIT"
export MICROGEN_OUTPUT_DIR="./projects"

microgen create my-project
```

## Best Practices

### Project Naming
- Use lowercase with hyphens: `user-management-api`
- Avoid reserved names: `test`, `src`, `dist`
- Keep names descriptive but concise

### Directory Structure
- Organize projects by domain: `./services/user-service`
- Separate frontend and backend: `./apps/web`, `./services/api`
- Use consistent naming conventions

### Configuration Management
- Use environment variables for secrets
- Version control configuration templates
- Document custom settings for team members

## Troubleshooting

### Common Issues

**Permission Errors:**
```bash
# Fix with proper permissions
sudo chown -R $USER:$USER ./project-directory
```

**Port Conflicts:**
```bash
# Check for running services
lsof -i :3000
lsof -i :8000
```

**Docker Issues:**
```bash
# Clean up containers and images
docker system prune -a
```

## Next Steps

- [**Backend Configuration**](./backend-config) - Configure backend services
- [**Frontend Configuration**](./frontend-config) - Setup frontend applications
- [**Microservices Setup**](./microservices) - Inter-service communication
- [**Examples**](../examples/basic-api) - See configuration in action
