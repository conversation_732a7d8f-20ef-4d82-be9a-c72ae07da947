---
sidebar_position: 3
---

# Frontend Configuration

Configure your frontend applications with modern frameworks, styling solutions, and build optimizations.

## Supported Frameworks

### Next.js (Recommended)
The React framework for production with built-in optimizations.

**Features:**
- Server-side rendering (SSR)
- Static site generation (SSG)
- API routes
- Image optimization
- Automatic code splitting
- TypeScript support

```bash
microgen create my-app --template frontend-only
# Select Next.js during setup
```

### React
A JavaScript library for building user interfaces.

**Features:**
- Component-based architecture
- Virtual DOM
- Hooks and context
- Rich ecosystem
- Developer tools

### Vue.js
The progressive JavaScript framework.

**Features:**
- Approachable learning curve
- Versatile ecosystem
- Performant rendering
- Composition API
- Single-file components

### Angular
Platform for building mobile and desktop web applications.

**Features:**
- Full framework solution
- TypeScript by default
- Dependency injection
- RxJS integration
- CLI tooling

### Svelte
Cybernetically enhanced web apps with compile-time optimizations.

**Features:**
- No virtual DOM
- Smaller bundle sizes
- Built-in state management
- Reactive programming
- Easy learning curve

## Styling Solutions

### Tailwind CSS v4 (<PERSON><PERSON>ult)
Utility-first CSS framework for rapid UI development.

**Features:**
- Utility classes
- Responsive design
- Dark mode support
- Custom design system
- JIT compilation
- Plugin ecosystem

### Styled Components
CSS-in-JS library for styling React components.

**Features:**
- Component-scoped styles
- Dynamic styling
- Theme support
- Server-side rendering
- TypeScript integration

### CSS Modules
Localized CSS for modular styling.

**Features:**
- Scoped styles
- Composition
- Build-time processing
- Framework agnostic
- No runtime overhead

### Sass/SCSS
CSS extension language with powerful features.

**Features:**
- Variables and mixins
- Nested rules
- Partials and imports
- Built-in functions
- Mature ecosystem

## Build Configuration

### Vite (Default)
Next-generation frontend tooling for fast development.

**Features:**
- Lightning-fast HMR
- Native ES modules
- Optimized builds
- Plugin ecosystem
- TypeScript support

### Webpack
Powerful module bundler for modern JavaScript applications.

**Features:**
- Code splitting
- Tree shaking
- Asset optimization
- Plugin system
- Development server

## State Management

### Built-in Solutions
Framework-specific state management.

**React:**
- Context API
- useReducer hook
- Custom hooks

**Vue:**
- Vuex/Pinia
- Composition API
- Reactive refs

**Angular:**
- Services
- RxJS
- NgRx (optional)

### External Libraries
Popular state management solutions.

**Options:**
- Redux Toolkit
- Zustand
- Jotai
- Recoil

## API Integration

### HTTP Clients
Configured API clients for backend communication.

**Axios Configuration:**
```typescript
import axios from 'axios';

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

### GraphQL Support
Optional GraphQL integration.

**Features:**
- Apollo Client
- Code generation
- Caching
- Subscriptions

## Authentication

### JWT Integration
Frontend authentication with JWT tokens.

**Features:**
- Token storage
- Automatic refresh
- Route protection
- User context

### Protected Routes
Route-based authentication guards.

```typescript
// Next.js example
import { useAuth } from '../hooks/useAuth';
import { useRouter } from 'next/router';

export function ProtectedRoute({ children }) {
  const { user, loading } = useAuth();
  const router = useRouter();

  if (loading) return <div>Loading...</div>;
  
  if (!user) {
    router.push('/login');
    return null;
  }

  return children;
}
```

## Performance Optimization

### Code Splitting
Automatic and manual code splitting strategies.

**Features:**
- Route-based splitting
- Component lazy loading
- Dynamic imports
- Bundle analysis

### Image Optimization
Optimized image handling and delivery.

**Features:**
- Next.js Image component
- WebP conversion
- Responsive images
- Lazy loading

### Caching Strategies
Client-side caching for better performance.

**Types:**
- Browser caching
- Service worker caching
- API response caching
- Static asset caching

## Development Tools

### TypeScript Configuration
Strict TypeScript setup for better development experience.

```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx"
  }
}
```

### ESLint & Prettier
Code quality and formatting tools.

**Features:**
- Consistent code style
- Error prevention
- Automatic formatting
- Git hooks integration

### Testing Setup
Comprehensive testing configuration.

**Tools:**
- Jest
- React Testing Library
- Cypress (E2E)
- Storybook (Component testing)

## Environment Configuration

### Development
```env
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_ENABLE_ANALYTICS=false
```

### Production
```env
NEXT_PUBLIC_API_URL=https://api.myapp.com
NEXT_PUBLIC_APP_ENV=production
NEXT_PUBLIC_ENABLE_ANALYTICS=true
```

## Docker Configuration

### Multi-stage Dockerfile
Optimized builds for different environments.

```dockerfile
# Dependencies
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci

# Builder
FROM node:18-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

# Runner
FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
EXPOSE 3000
CMD ["node", "server.js"]
```

## Deployment Options

### Static Deployment
For static sites and SPAs.

**Platforms:**
- Vercel
- Netlify
- GitHub Pages
- AWS S3 + CloudFront

### Server-side Deployment
For SSR applications.

**Platforms:**
- Vercel
- Railway
- Heroku
- AWS ECS
- Docker containers

## Next Steps

- [**Backend Configuration**](./backend-config) - Configure backend services
- [**Microservices Setup**](./microservices) - Inter-service communication
- [**Project Configuration**](./project-config) - Overall project setup
- [**Examples**](../examples/basic-api) - See frontend configuration in action
