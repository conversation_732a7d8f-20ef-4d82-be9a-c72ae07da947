import fs from 'fs-extra';
import path from 'path';
import { Logger } from './logger.js';

/**
 * File system utilities for the CLI
 */
export class FileUtils {
  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  /**
   * Check if a directory exists and is not empty
   */
  async isDirectoryEmpty(dirPath: string): Promise<boolean> {
    try {
      const exists = await fs.pathExists(dirPath);
      if (!exists) return true;

      const files = await fs.readdir(dirPath);
      return files.length === 0;
    } catch (error) {
      this.logger.debug(`Error checking directory: ${error}`);
      return true;
    }
  }

  /**
   * Ensure directory exists, create if it doesn't
   */
  async ensureDirectory(dirPath: string): Promise<void> {
    try {
      await fs.ensureDir(dirPath);
      this.logger.debug(`Ensured directory exists: ${dirPath}`);
    } catch (error) {
      throw new Error(`Failed to create directory ${dirPath}: ${error}`);
    }
  }

  /**
   * Copy file with optional template processing
   */
  async copyFile(source: string, destination: string): Promise<void> {
    try {
      await fs.copy(source, destination);
      this.logger.debug(`Copied file: ${source} -> ${destination}`);
    } catch (error) {
      throw new Error(`Failed to copy file ${source} to ${destination}: ${error}`);
    }
  }

  /**
   * Write content to file
   */
  async writeFile(filePath: string, content: string): Promise<void> {
    try {
      await fs.ensureDir(path.dirname(filePath));
      await fs.writeFile(filePath, content, 'utf8');
      this.logger.debug(`Wrote file: ${filePath}`);
    } catch (error) {
      throw new Error(`Failed to write file ${filePath}: ${error}`);
    }
  }

  /**
   * Read file content
   */
  async readFile(filePath: string): Promise<string> {
    try {
      return await fs.readFile(filePath, 'utf8');
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error}`);
    }
  }

  /**
   * Check if file exists
   */
  async fileExists(filePath: string): Promise<boolean> {
    try {
      return await fs.pathExists(filePath);
    } catch (error) {
      this.logger.debug(`Error checking file existence: ${error}`);
      return false;
    }
  }

  /**
   * Get relative path from current working directory
   */
  getRelativePath(filePath: string): string {
    return path.relative(process.cwd(), filePath);
  }

  /**
   * Resolve path relative to project root
   */
  resolvePath(...pathSegments: string[]): string {
    return path.resolve(...pathSegments);
  }

  /**
   * Get file extension
   */
  getExtension(filePath: string): string {
    return path.extname(filePath);
  }

  /**
   * Get filename without extension
   */
  getBasename(filePath: string): string {
    return path.basename(filePath, path.extname(filePath));
  }
}
