import Mustache from 'mustache';
import path from 'path';
import { Logger } from '../utils/logger.js';
import { FileUtils } from '../utils/file-utils.js';
import { TemplateContext, TemplateFile } from '../types/index.js';

/**
 * Template engine for processing template files
 */
export class TemplateEngine {
  private logger: Logger;
  private fileUtils: FileUtils;

  constructor(logger: Logger, fileUtils: FileUtils) {
    this.logger = logger;
    this.fileUtils = fileUtils;
  }

  /**
   * Process a template file with the given context
   */
  async processTemplate(templatePath: string, context: TemplateContext): Promise<string> {
    try {
      const templateContent = await this.fileUtils.readFile(templatePath);
      return Mustache.render(templateContent, context);
    } catch (error) {
      throw new Error(`Failed to process template ${templatePath}: ${error}`);
    }
  }

  /**
   * Process multiple template files
   */
  async processTemplates(
    templates: TemplateFile[],
    context: TemplateContext,
    outputDir: string
  ): Promise<void> {
    this.logger.startSpinner('Processing templates...');

    for (const template of templates) {
      try {
        const sourcePath = this.getTemplatePath(template.source);
        const destinationPath = path.join(outputDir, template.destination);

        if (template.isTemplate) {
          // Process as Mustache template
          const processedContent = await this.processTemplate(sourcePath, context);
          await this.fileUtils.writeFile(destinationPath, processedContent);
        } else {
          // Copy file as-is
          await this.fileUtils.copyFile(sourcePath, destinationPath);
        }

        this.logger.debug(`Processed: ${template.source} -> ${template.destination}`);
      } catch (error) {
        this.logger.failSpinner(`Failed to process template: ${template.source}`);
        throw error;
      }
    }

    this.logger.succeedSpinner('Templates processed successfully');
  }

  /**
   * Get the full path to a template file
   */
  private getTemplatePath(templateName: string): string {
    return path.join(__dirname, '../../templates', templateName);
  }

  /**
   * Create template context with common variables
   */
  createContext(baseContext: Partial<TemplateContext>): TemplateContext {
    const now = new Date();
    
    return {
      timestamp: now.toISOString(),
      year: now.getFullYear(),
      project: {
        name: '',
        description: '',
        version: '1.0.0',
        license: 'MIT',
        ...baseContext.project,
      },
      ...baseContext,
    } as TemplateContext;
  }

  /**
   * Validate template context
   */
  validateContext(context: TemplateContext): void {
    if (!context.project?.name) {
      throw new Error('Project name is required in template context');
    }

    // Add more validation as needed
  }

  /**
   * Get available templates
   */
  async getAvailableTemplates(): Promise<string[]> {
    const templatesDir = path.join(__dirname, '../../templates');
    
    try {
      const exists = await this.fileUtils.fileExists(templatesDir);
      if (!exists) {
        return [];
      }

      // This would be implemented to scan template directories
      // For now, return hardcoded list
      return ['express-api', 'react-frontend', 'docker-compose'];
    } catch (error) {
      this.logger.debug(`Error getting available templates: ${error}`);
      return [];
    }
  }
}
