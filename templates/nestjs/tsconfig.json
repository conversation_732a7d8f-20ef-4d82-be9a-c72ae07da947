{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@/config/*": ["src/config/*"], "@/database/*": ["src/database/*"], "@/auth/*": ["src/auth/*"], "@/microservice/*": ["src/microservice/*"], "@/health/*": ["src/health/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}