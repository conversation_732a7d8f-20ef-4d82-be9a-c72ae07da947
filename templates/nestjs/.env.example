# Application Configuration
NODE_ENV=development
PORT={{service.port}}

# CORS Configuration
{{#service.cors}}
CORS_ORIGIN=*
{{/service.cors}}

{{#service.database}}
# Database Configuration
{{#if (eq service.database "postgresql")}}
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_NAME={{service.name}}_db
{{/if}}
{{#if (eq service.database "mysql")}}
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_NAME={{service.name}}_db
{{/if}}
{{#if (eq service.database "sqlite")}}
DB_PATH=./{{service.name}}.db
{{/if}}
{{#if (eq service.database "mongodb")}}
MONGODB_URI=mongodb://localhost:27017/{{service.name}}_db
{{/if}}
{{/service.database}}

{{#service.authentication}}
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
{{/service.authentication}}

{{#service.microserviceTransport}}
# Microservice Transport Configuration
{{#each service.microserviceTransport}}
{{#if (eq this "kafka")}}
# Kafka Configuration
KAFKA_BROKER=localhost:9092
{{/if}}
{{#if (eq this "grpc")}}
# gRPC Configuration
GRPC_URL=0.0.0.0:50051
{{/if}}
{{#if (eq this "redis")}}
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
{{/if}}
{{#if (eq this "tcp")}}
# TCP Configuration
TCP_HOST=localhost
TCP_PORT=3001
{{/if}}
{{#if (eq this "nats")}}
# NATS Configuration
NATS_URL=nats://localhost:4222
{{/if}}
{{#if (eq this "rabbitmq")}}
# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
{{/if}}
{{/each}}
{{/service.microserviceTransport}}

# Logging Configuration
LOG_LEVEL=info

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000

# Rate Limiting Configuration
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Security Configuration
BCRYPT_SALT_ROUNDS=12

# Email Configuration (if needed)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# External Services (examples)
# EXTERNAL_API_URL=https://api.example.com
# EXTERNAL_API_KEY=your-api-key
