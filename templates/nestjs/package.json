{
  "name": "{{project.name}}-{{service.name}}",
  "version": "{{project.version}}",
  "description": "{{service.name}} microservice built with NestJS",
  "author": "{{project.author}}",
  "private": true,
  "license": "{{project.license}}",
  "scripts": {
    "build": "nest build",
    "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"",
    "start": "nest start",
    "start:dev": "nest start --watch",
    "start:debug": "nest start --debug --watch",
    "start:prod": "node dist/main",
    "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:cov": "jest --coverage",
    "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
    "test:e2e": "jest --config ./test/jest-e2e.json",
    "migration:generate": "typeorm-ts-node-commonjs migration:generate",
    "migration:run": "typeorm-ts-node-commonjs migration:run",
    "migration:revert": "typeorm-ts-node-commonjs migration:revert",
    "schema:drop": "typeorm-ts-node-commonjs schema:drop",
    "docker:build": "docker build -t {{project.name}}-{{service.name}} .",
    "docker:run": "docker run -p {{service.port}}:{{service.port}} {{project.name}}-{{service.name}}"
  },
  "dependencies": {
    "@nestjs/common": "^10.0.0",
    "@nestjs/core": "^10.0.0",
    "@nestjs/platform-express": "^10.0.0",
    "@nestjs/config": "^3.0.0",
    "@nestjs/terminus": "^10.0.0",
    "@nestjs/axios": "^3.0.0",
    "reflect-metadata": "^0.1.13",
    "rxjs": "^7.8.1",
    "class-validator": "^0.14.0",
    "class-transformer": "^0.5.1",
    {{#service.authentication}}
    "@nestjs/jwt": "^10.0.0",
    "@nestjs/passport": "^10.0.0",
    "passport": "^0.6.0",
    "passport-jwt": "^4.0.1",
    "passport-local": "^1.0.0",
    "bcryptjs": "^2.4.3",
    {{/service.authentication}}
    {{#service.swagger}}
    "@nestjs/swagger": "^7.0.0",
    {{/service.swagger}}
    {{#service.database}}
    {{#if (or (eq service.database "postgresql") (eq service.database "mysql") (eq service.database "sqlite"))}}
    "@nestjs/typeorm": "^10.0.0",
    "typeorm": "^0.3.17",
    {{#if (eq service.database "postgresql")}}
    "pg": "^8.11.0",
    {{/if}}
    {{#if (eq service.database "mysql")}}
    "mysql2": "^3.6.0",
    {{/if}}
    {{#if (eq service.database "sqlite")}}
    "sqlite3": "^5.1.6",
    {{/if}}
    {{/if}}
    {{#if (eq service.database "mongodb")}}
    "@nestjs/mongoose": "^10.0.0",
    "mongoose": "^7.4.0",
    "mongoose-autopopulate": "^1.0.0",
    {{/if}}
    {{/service.database}}
    {{#service.microserviceTransport}}
    "@nestjs/microservices": "^10.0.0",
    {{#each service.microserviceTransport}}
    {{#if (eq this "kafka")}}
    "kafkajs": "^2.2.4",
    {{/if}}
    {{#if (eq this "grpc")}}
    "@grpc/grpc-js": "^1.9.0",
    "@grpc/proto-loader": "^0.7.8",
    {{/if}}
    {{#if (eq this "redis")}}
    "redis": "^4.6.7",
    {{/if}}
    {{#if (eq this "nats")}}
    "nats": "^2.15.1",
    {{/if}}
    {{#if (eq this "rabbitmq")}}
    "amqplib": "^0.10.3",
    "amqp-connection-manager": "^4.1.14",
    {{/if}}
    {{/each}}
    {{/service.microserviceTransport}}
    {{#service.cors}}
    "cors": "^2.8.5",
    {{/service.cors}}
    "helmet": "^7.0.0",
    "compression": "^1.7.4",
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "@nestjs/cli": "^10.0.0",
    "@nestjs/schematics": "^10.0.0",
    "@nestjs/testing": "^10.0.0",
    "@types/express": "^4.17.17",
    "@types/jest": "^29.5.2",
    "@types/node": "^20.3.1",
    "@types/supertest": "^2.0.12",
    {{#service.authentication}}
    "@types/passport-jwt": "^3.0.9",
    "@types/passport-local": "^1.0.35",
    "@types/bcryptjs": "^2.4.2",
    {{/service.authentication}}
    {{#service.database}}
    {{#if (eq service.database "postgresql")}}
    "@types/pg": "^8.10.2",
    {{/if}}
    {{#if (eq service.database "mongodb")}}
    "@types/mongoose": "^5.11.97",
    {{/if}}
    {{#if (eq service.database "rabbitmq")}}
    "@types/amqplib": "^0.10.1",
    {{/if}}
    {{/service.database}}
    {{#service.cors}}
    "@types/cors": "^2.8.13",
    {{/service.cors}}
    "@types/compression": "^1.7.2",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "eslint": "^8.42.0",
    "eslint-config-prettier": "^8.8.0",
    "eslint-plugin-prettier": "^4.2.1",
    "jest": "^29.5.0",
    "prettier": "^2.8.8",
    "source-map-support": "^0.5.21",
    "supertest": "^6.3.3",
    "ts-jest": "^29.1.0",
    "ts-loader": "^9.4.3",
    "ts-node": "^10.9.1",
    "tsconfig-paths": "^4.2.0",
    "typescript": "^5.1.3"
  },
  "jest": {
    "moduleFileExtensions": [
      "js",
      "json",
      "ts"
    ],
    "rootDir": "src",
    "testRegex": ".*\\.spec\\.ts$",
    "transform": {
      "^.+\\.(t|j)s$": "ts-jest"
    },
    "collectCoverageFrom": [
      "**/*.(t|j)s"
    ],
    "coverageDirectory": "../coverage",
    "testEnvironment": "node"
  },
  "engines": {
    "node": ">=16.0.0",
    "npm": ">=8.0.0"
  }
}
