version: '3.8'

services:
  # Main application service
  {{service.name}}:
    build:
      context: .
      target: dev
    ports:
      - "{{service.port}}:{{service.port}}"
    environment:
      - NODE_ENV=development
      - PORT={{service.port}}
      {{#service.database}}
      {{#if (eq service.database "postgresql")}}
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - DB_NAME={{service.name}}_db
      {{/if}}
      {{#if (eq service.database "mysql")}}
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=password
      - DB_NAME={{service.name}}_db
      {{/if}}
      {{#if (eq service.database "mongodb")}}
      - MONGODB_URI=mongodb://mongo:27017/{{service.name}}_db
      {{/if}}
      {{/service.database}}
      {{#service.microserviceTransport}}
      {{#each service.microserviceTransport}}
      {{#if (eq this "kafka")}}
      - KAFKA_BROKER=kafka:9092
      {{/if}}
      {{#if (eq this "redis")}}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      {{/if}}
      {{#if (eq this "nats")}}
      - NATS_URL=nats://nats:4222
      {{/if}}
      {{#if (eq this "rabbitmq")}}
      - RABBITMQ_URL=amqp://rabbitmq:5672
      {{/if}}
      {{/each}}
      {{/service.microserviceTransport}}
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      {{#service.database}}
      {{#if (eq service.database "postgresql")}}
      - postgres
      {{/if}}
      {{#if (eq service.database "mysql")}}
      - mysql
      {{/if}}
      {{#if (eq service.database "mongodb")}}
      - mongo
      {{/if}}
      {{/service.database}}
      {{#service.microserviceTransport}}
      {{#each service.microserviceTransport}}
      {{#if (eq this "kafka")}}
      - kafka
      - zookeeper
      {{/if}}
      {{#if (eq this "redis")}}
      - redis
      {{/if}}
      {{#if (eq this "nats")}}
      - nats
      {{/if}}
      {{#if (eq this "rabbitmq")}}
      - rabbitmq
      {{/if}}
      {{/each}}
      {{/service.microserviceTransport}}
    networks:
      - {{service.name}}-network

  {{#service.database}}
  {{#if (eq service.database "postgresql")}}
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB={{service.name}}_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - {{service.name}}-network
  {{/if}}

  {{#if (eq service.database "mysql")}}
  # MySQL Database
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE={{service.name}}_db
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - {{service.name}}-network
  {{/if}}

  {{#if (eq service.database "mongodb")}}
  # MongoDB Database
  mongo:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    networks:
      - {{service.name}}-network
  {{/if}}
  {{/service.database}}

  {{#service.microserviceTransport}}
  {{#each service.microserviceTransport}}
  {{#if (eq this "kafka")}}
  # Zookeeper for Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      - ZOOKEEPER_CLIENT_PORT=2181
      - ZOOKEEPER_TICK_TIME=2000
    networks:
      - {{../service.name}}-network

  # Kafka Message Broker
  kafka:
    image: confluentinc/cp-kafka:latest
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      - KAFKA_BROKER_ID=1
      - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
    networks:
      - {{../service.name}}-network
  {{/if}}

  {{#if (eq this "redis")}}
  # Redis Cache/Message Broker
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - {{../service.name}}-network
  {{/if}}

  {{#if (eq this "nats")}}
  # NATS Message Broker
  nats:
    image: nats:2.9-alpine
    ports:
      - "4222:4222"
      - "8222:8222"
    networks:
      - {{../service.name}}-network
  {{/if}}

  {{#if (eq this "rabbitmq")}}
  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3.11-management-alpine
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=password
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - {{../service.name}}-network
  {{/if}}
  {{/each}}
  {{/service.microserviceTransport}}

networks:
  {{service.name}}-network:
    driver: bridge

volumes:
  {{#service.database}}
  {{#if (eq service.database "postgresql")}}
  postgres_data:
  {{/if}}
  {{#if (eq service.database "mysql")}}
  mysql_data:
  {{/if}}
  {{#if (eq service.database "mongodb")}}
  mongo_data:
  {{/if}}
  {{/service.database}}
  {{#service.microserviceTransport}}
  {{#each service.microserviceTransport}}
  {{#if (eq this "redis")}}
  redis_data:
  {{/if}}
  {{#if (eq this "rabbitmq")}}
  rabbitmq_data:
  {{/if}}
  {{/each}}
  {{/service.microserviceTransport}}
