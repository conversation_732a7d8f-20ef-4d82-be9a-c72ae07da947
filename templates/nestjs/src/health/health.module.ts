import { Modu<PERSON> } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';
import { HealthController } from './health.controller.js';
import { HealthService } from './health.service.js';
{{#service.database}}
{{#if (eq service.database "postgresql")}}
import { TypeOrmModule } from '@nestjs/typeorm';
{{/if}}
{{#if (eq service.database "mysql")}}
import { TypeOrmModule } from '@nestjs/typeorm';
{{/if}}
{{#if (eq service.database "mongodb")}}
import { MongooseModule } from '@nestjs/mongoose';
{{/if}}
{{#if (eq service.database "sqlite")}}
import { TypeOrmModule } from '@nestjs/typeorm';
{{/if}}
{{/service.database}}

@Module({
  imports: [
    TerminusModule,
    HttpModule,
    {{#service.database}}
    {{#if (or (eq service.database "postgresql") (eq service.database "mysql") (eq service.database "sqlite"))}}
    TypeOrmModule.forFeature([]),
    {{/if}}
    {{#if (eq service.database "mongodb")}}
    MongooseModule.forFeature([]),
    {{/if}}
    {{/service.database}}
  ],
  controllers: [HealthController],
  providers: [HealthService],
  exports: [HealthService],
})
export class HealthModule {}
