import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  HealthCheckService,
  HealthCheck,
  HealthCheckResult,
  {{#service.database}}
  {{#if (or (eq service.database "postgresql") (eq service.database "mysql") (eq service.database "sqlite"))}}
  TypeOrmHealthIndicator,
  {{/if}}
  {{#if (eq service.database "mongodb")}}
  MongooseHealthIndicator,
  {{/if}}
  {{/service.database}}
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from '@nestjs/terminus';
import { HealthService } from './health.service.js';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly healthCheckService: HealthCheckService,
    private readonly healthService: HealthService,
    {{#service.database}}
    {{#if (or (eq service.database "postgresql") (eq service.database "mysql") (eq service.database "sqlite"))}}
    private readonly typeOrmHealthIndicator: TypeOrmHealthIndicator,
    {{/if}}
    {{#if (eq service.database "mongodb")}}
    private readonly mongooseHealthIndicator: MongooseHealthIndicator,
    {{/if}}
    {{/service.database}}
    private readonly memoryHealthIndicator: MemoryHealthIndicator,
    private readonly diskHealthIndicator: DiskHealthIndicator,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get overall health status' })
  @ApiResponse({ status: 200, description: 'Health check successful' })
  @ApiResponse({ status: 503, description: 'Health check failed' })
  @HealthCheck()
  async check(): Promise<HealthCheckResult> {
    return this.healthCheckService.check([
      // Memory check - ensure we're not using too much memory
      () => this.memoryHealthIndicator.checkHeap('memory_heap', 150 * 1024 * 1024),
      () => this.memoryHealthIndicator.checkRSS('memory_rss', 150 * 1024 * 1024),
      
      // Disk space check
      () => this.diskHealthIndicator.checkStorage('storage', {
        path: '/',
        thresholdPercent: 0.9,
      }),

      {{#service.database}}
      // Database health check
      {{#if (or (eq service.database "postgresql") (eq service.database "mysql") (eq service.database "sqlite"))}}
      () => this.typeOrmHealthIndicator.pingCheck('database'),
      {{/if}}
      {{#if (eq service.database "mongodb")}}
      () => this.mongooseHealthIndicator.pingCheck('database'),
      {{/if}}
      {{/service.database}}

      // Custom microservice health checks
      () => this.healthService.checkMicroserviceHealth(),
    ]);
  }

  @Get('ready')
  @ApiOperation({ summary: 'Readiness probe - check if service is ready to handle requests' })
  @ApiResponse({ status: 200, description: 'Service is ready' })
  @ApiResponse({ status: 503, description: 'Service is not ready' })
  @HealthCheck()
  async readiness(): Promise<HealthCheckResult> {
    return this.healthCheckService.check([
      {{#service.database}}
      // Database must be available for readiness
      {{#if (or (eq service.database "postgresql") (eq service.database "mysql") (eq service.database "sqlite"))}}
      () => this.typeOrmHealthIndicator.pingCheck('database'),
      {{/if}}
      {{#if (eq service.database "mongodb")}}
      () => this.mongooseHealthIndicator.pingCheck('database'),
      {{/if}}
      {{/service.database}}
      
      // Check if microservice transports are ready
      () => this.healthService.checkTransportReadiness(),
    ]);
  }

  @Get('live')
  @ApiOperation({ summary: 'Liveness probe - check if service is alive' })
  @ApiResponse({ status: 200, description: 'Service is alive' })
  @ApiResponse({ status: 503, description: 'Service is not alive' })
  @HealthCheck()
  async liveness(): Promise<HealthCheckResult> {
    return this.healthCheckService.check([
      // Basic liveness check - just ensure the service is running
      () => this.healthService.checkBasicLiveness(),
    ]);
  }

  @Get('detailed')
  @ApiOperation({ summary: 'Get detailed health information' })
  @ApiResponse({ status: 200, description: 'Detailed health information' })
  async getDetailedHealth() {
    return this.healthService.getDetailedHealthInfo();
  }
}
