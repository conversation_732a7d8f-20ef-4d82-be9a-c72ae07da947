import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HealthIndicatorResult, HealthIndicator } from '@nestjs/terminus';

interface ServiceHealth {
  status: 'up' | 'down';
  timestamp: string;
  uptime: number;
  memory: NodeJS.MemoryUsage;
  version: string;
  environment: string;
  {{#service.microserviceTransport}}
  transports: {
    {{#each service.microserviceTransport}}
    {{this}}: {
      status: 'connected' | 'disconnected' | 'error';
      lastCheck: string;
      details?: any;
    };
    {{/each}}
  };
  {{/service.microserviceTransport}}
  {{#service.database}}
  database: {
    status: 'connected' | 'disconnected' | 'error';
    type: '{{service.database}}';
    lastCheck: string;
  };
  {{/service.database}}
}

@Injectable()
export class HealthService extends HealthIndicator {
  private readonly logger = new Logger(HealthService.name);

  constructor(private readonly configService: ConfigService) {
    super();
  }

  /**
   * Basic liveness check - just confirms the service is running
   */
  async checkBasicLiveness(): Promise<HealthIndicatorResult> {
    const isHealthy = true; // If we can execute this, we're alive
    
    const result = this.getStatus('{{service.name}}_liveness', isHealthy, {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    });

    return result;
  }

  /**
   * Check microservice transport health
   */
  async checkMicroserviceHealth(): Promise<HealthIndicatorResult> {
    try {
      const transportChecks = await this.checkAllTransports();
      const allHealthy = Object.values(transportChecks).every(
        (transport: any) => transport.status === 'connected'
      );

      const result = this.getStatus('microservices', allHealthy, {
        transports: transportChecks,
        timestamp: new Date().toISOString(),
      });

      return result;
    } catch (error) {
      this.logger.error('Microservice health check failed:', error);
      return this.getStatus('microservices', false, {
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Check transport readiness
   */
  async checkTransportReadiness(): Promise<HealthIndicatorResult> {
    try {
      {{#service.microserviceTransport}}
      const transportChecks = await this.checkAllTransports();
      const allReady = Object.values(transportChecks).every(
        (transport: any) => transport.status === 'connected'
      );

      return this.getStatus('transport_readiness', allReady, {
        transports: transportChecks,
        timestamp: new Date().toISOString(),
      });
      {{else}}
      // No microservice transports configured
      return this.getStatus('transport_readiness', true, {
        message: 'No transports configured',
        timestamp: new Date().toISOString(),
      });
      {{/service.microserviceTransport}}
    } catch (error) {
      this.logger.error('Transport readiness check failed:', error);
      return this.getStatus('transport_readiness', false, {
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get detailed health information
   */
  async getDetailedHealthInfo(): Promise<ServiceHealth> {
    const healthInfo: ServiceHealth = {
      status: 'up',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '1.0.0',
      environment: this.configService.get('nodeEnv', 'development'),
      {{#service.microserviceTransport}}
      transports: await this.checkAllTransports(),
      {{/service.microserviceTransport}}
      {{#service.database}}
      database: await this.checkDatabaseHealth(),
      {{/service.database}}
    };

    return healthInfo;
  }

  {{#service.microserviceTransport}}
  /**
   * Check all configured transports
   */
  private async checkAllTransports(): Promise<any> {
    const transportChecks: any = {};

    {{#each service.microserviceTransport}}
    // Check {{this}} transport
    try {
      transportChecks.{{this}} = await this.check{{pascalCase this}}Health();
    } catch (error) {
      this.logger.error('{{pascalCase this}} health check failed:', error);
      transportChecks.{{this}} = {
        status: 'error',
        lastCheck: new Date().toISOString(),
        error: error.message,
      };
    }
    {{/each}}

    return transportChecks;
  }

  {{#each service.microserviceTransport}}
  /**
   * Check {{this}} transport health
   */
  private async check{{pascalCase this}}Health(): Promise<any> {
    // TODO: Implement actual {{this}} health check
    // This is a placeholder implementation
    return {
      status: 'connected',
      lastCheck: new Date().toISOString(),
      details: {
        transport: '{{this}}',
        {{#if (eq this "kafka")}}
        brokers: this.configService.get('microservices.kafka.brokers', ['localhost:9092']),
        {{/if}}
        {{#if (eq this "grpc")}}
        url: this.configService.get('microservices.grpc.url', 'localhost:50051'),
        {{/if}}
        {{#if (eq this "redis")}}
        host: this.configService.get('microservices.redis.host', 'localhost'),
        port: this.configService.get('microservices.redis.port', 6379),
        {{/if}}
        {{#if (eq this "tcp")}}
        host: this.configService.get('microservices.tcp.host', 'localhost'),
        port: this.configService.get('microservices.tcp.port', 3001),
        {{/if}}
        {{#if (eq this "nats")}}
        servers: this.configService.get('microservices.nats.servers', ['nats://localhost:4222']),
        {{/if}}
        {{#if (eq this "rabbitmq")}}
        urls: this.configService.get('microservices.rabbitmq.urls', ['amqp://localhost:5672']),
        {{/if}}
      },
    };
  }
  {{/each}}
  {{/service.microserviceTransport}}

  {{#service.database}}
  /**
   * Check database health
   */
  private async checkDatabaseHealth(): Promise<any> {
    try {
      // TODO: Implement actual database health check
      // This is a placeholder implementation
      return {
        status: 'connected',
        type: '{{service.database}}',
        lastCheck: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      return {
        status: 'error',
        type: '{{service.database}}',
        lastCheck: new Date().toISOString(),
        error: error.message,
      };
    }
  }
  {{/service.database}}
}
