import { Injectable, ExecutionContext } from '@nestjs/common';
import { ThrottlerGuard } from '@nestjs/throttler';

@Injectable()
export class CustomThrottlerGuard extends ThrottlerGuard {
  protected getTracker(req: Record<string, any>): string {
    // Use IP address and user ID (if authenticated) for tracking
    const ip = req.ip || req.connection.remoteAddress;
    const userId = req.user?.id;
    
    return userId ? `${ip}-${userId}` : ip;
  }

  protected generateKey(context: ExecutionContext, tracker: string): string {
    const request = context.switchToHttp().getRequest();
    const route = `${request.method}-${request.route?.path || request.url}`;
    
    return `${tracker}-${route}`;
  }
}
