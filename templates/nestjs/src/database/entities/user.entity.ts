{{#if (or (eq service.database "postgresql") (eq service.database "mysql") (eq service.database "sqlite"))}}
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from './base.entity.js';

@Entity('users')
@Index(['email'], { unique: true })
export class User extends BaseEntity {
  @Column({ type: 'varchar', length: 255, unique: true })
  email: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', length: 255, select: false })
  password: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'varchar', length: 50, default: 'user' })
  role: string;

  @Column({ type: 'timestamp', nullable: true })
  lastLoginAt?: Date;

  @Column({ type: 'json', nullable: true })
  metadata?: Record<string, any>;
}
{{/if}}
{{#if (eq service.database "mongodb")}}
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { BaseDocument } from './base.entity.js';

@Schema({ collection: 'users' })
export class User extends BaseDocument {
  @Prop({ required: true, unique: true, index: true })
  email: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true, select: false })
  password: string;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: 'user' })
  role: string;

  @Prop({ default: null })
  lastLoginAt?: Date;

  @Prop({ type: Object, default: {} })
  metadata?: Record<string, any>;
}

export const UserSchema = SchemaFactory.createForClass(User);

// Add indexes
UserSchema.index({ email: 1 }, { unique: true });
UserSchema.index({ isActive: 1 });
UserSchema.index({ role: 1 });
{{/if}}
